import json
import os
import tkinter as tk
from tkinter import ttk, colorchooser, font
from pathlib import Path

class ConfigManager:
    """
    配置管理类，负责加载和保存应用程序配置
    """
    def __init__(self, config_path=None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        if config_path is None:
            # 获取项目根目录
            root_dir = Path(__file__).parent.parent.parent
            self.config_path = os.path.join(root_dir, 'config', 'settings.json')
        else:
            self.config_path = config_path
        
        # 加载配置
        self.config = self._load_config()
    
    def _load_config(self):
        """
        从文件加载配置
        
        Returns:
            dict: 配置字典
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"加载配置文件失败: {e}")
            # 返回默认配置
            return self._default_config()
    
    def _default_config(self):
        """
        返回默认配置
        
        Returns:
            dict: 默认配置字典
        """
        return {
            "app": {
                "name": "记事本",
                "version": "1.0.0",
                "language": "zh-CN"
            },
            "editor": {
                "font": {
                    "family": "Microsoft YaHei",
                    "size": 12,
                    "weight": "normal"
                },
                "theme": {
                    "foreground": "#000000",
                    "background": "#FFFFFF",
                    "selection": "#B5D5FF"
                },
                "behavior": {
                    "autoIndent": True,
                    "smartIndent": True,
                    "tabSize": 4,
                    "wordWrap": True
                }
            },
            "file": {
                "encoding": "UTF-8",
                "defaultExtension": ".txt",
                "recentFiles": [],
                "maxRecentFiles": 10
            },
            "window": {
                "width": 800,
                "height": 600,
                "startMaximized": False,
                "rememberSize": True,
                "showStatusBar": True
            }
        }
    
    def get(self, section, key=None):
        """
        获取配置值
        
        Args:
            section: 配置节名称
            key: 配置项名称，如果为None则返回整个节
            
        Returns:
            配置值或配置节字典
        """
        if section not in self.config:
            return None
        
        if key is None:
            return self.config[section]
        
        if key not in self.config[section]:
            return None
            
        return self.config[section][key]
    
    def set(self, section, key, value):
        """
        设置配置值
        
        Args:
            section: 配置节名称
            key: 配置项名称
            value: 配置值
        """
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
    
    def save(self):
        """
        保存配置到文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保配置目录存在
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def add_recent_file(self, file_path):
        """
        添加最近打开的文件
        
        Args:
            file_path: 文件路径
        """
        recent_files = self.get('file', 'recentFiles')
        max_recent = self.get('file', 'maxRecentFiles')
        
        if recent_files is None:
            recent_files = []
        
        # 如果文件已在列表中，先移除
        if file_path in recent_files:
            recent_files.remove(file_path)
        
        # 添加到列表开头
        recent_files.insert(0, file_path)
        
        # 保持列表长度不超过最大值
        if max_recent and len(recent_files) > max_recent:
            recent_files = recent_files[:max_recent]
        
        self.set('file', 'recentFiles', recent_files)
        self.save()


class ConfigDialog:
    """
    配置对话框类，提供GUI界面让用户修改配置
    """
    def __init__(self, parent, config_manager, callback=None):
        """
        初始化配置对话框
        
        Args:
            parent: 父窗口
            config_manager: 配置管理器实例
            callback: 配置更改后的回调函数
        """
        self.parent = parent
        self.config_manager = config_manager
        self.callback = callback
        self.result = False
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("记事本设置")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 设置窗口图标
        try:
            icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                    "resources", "icons", "notepad.ico")
            if os.path.exists(icon_path):
                self.dialog.iconbitmap(icon_path)
        except:
            pass
        
        # 创建配置选项卡
        self.notebook = ttk.Notebook(self.dialog)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建各选项卡页面
        self._create_general_tab()
        self._create_editor_tab()
        self._create_appearance_tab()
        self._create_file_tab()
        
        # 创建底部按钮
        self._create_buttons()
        
        # 对话框关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)
        
        # 加载当前配置
        self._load_current_config()
        
        # 等待对话框关闭
        parent.wait_window(self.dialog)
    
    def _create_general_tab(self):
        """创建常规选项卡"""
        general_frame = ttk.Frame(self.notebook)
        self.notebook.add(general_frame, text="常规")
        
        # 窗口选项
        window_frame = ttk.LabelFrame(general_frame, text="窗口")
        window_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 启动时最大化
        self.start_maximized_var = tk.BooleanVar()
        ttk.Checkbutton(window_frame, text="启动时最大化", variable=self.start_maximized_var).pack(anchor=tk.W, padx=10, pady=5)
        
        # 记住窗口大小
        self.remember_size_var = tk.BooleanVar()
        ttk.Checkbutton(window_frame, text="记住窗口大小", variable=self.remember_size_var).pack(anchor=tk.W, padx=10, pady=5)
        
        # 显示状态栏
        self.show_status_bar_var = tk.BooleanVar()
        ttk.Checkbutton(window_frame, text="显示状态栏", variable=self.show_status_bar_var).pack(anchor=tk.W, padx=10, pady=5)
        
        # 窗口尺寸
        size_frame = ttk.Frame(window_frame)
        size_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(size_frame, text="默认窗口宽度:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.window_width_var = tk.IntVar()
        ttk.Spinbox(size_frame, from_=400, to=2000, width=6, textvariable=self.window_width_var).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(size_frame, text="默认窗口高度:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.window_height_var = tk.IntVar()
        ttk.Spinbox(size_frame, from_=300, to=1500, width=6, textvariable=self.window_height_var).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 语言设置
        language_frame = ttk.LabelFrame(general_frame, text="语言")
        language_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(language_frame, text="界面语言:").pack(side=tk.LEFT, padx=10, pady=5)
        self.language_var = tk.StringVar()
        language_combo = ttk.Combobox(language_frame, textvariable=self.language_var, state="readonly", width=10)
        language_combo["values"] = ("zh-CN", "en-US")
        language_combo.pack(side=tk.LEFT, padx=5, pady=5)
    
    def _create_editor_tab(self):
        """创建编辑器选项卡"""
        editor_frame = ttk.Frame(self.notebook)
        self.notebook.add(editor_frame, text="编辑器")
        
        # 行为选项
        behavior_frame = ttk.LabelFrame(editor_frame, text="行为")
        behavior_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 自动缩进
        self.auto_indent_var = tk.BooleanVar()
        ttk.Checkbutton(behavior_frame, text="自动缩进", variable=self.auto_indent_var).pack(anchor=tk.W, padx=10, pady=5)
        
        # 智能缩进
        self.smart_indent_var = tk.BooleanVar()
        ttk.Checkbutton(behavior_frame, text="智能缩进", variable=self.smart_indent_var).pack(anchor=tk.W, padx=10, pady=5)
        
        # 自动换行
        self.word_wrap_var = tk.BooleanVar()
        ttk.Checkbutton(behavior_frame, text="自动换行", variable=self.word_wrap_var).pack(anchor=tk.W, padx=10, pady=5)
        
        # Tab大小
        tab_frame = ttk.Frame(behavior_frame)
        tab_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(tab_frame, text="Tab大小:").pack(side=tk.LEFT, padx=5)
        self.tab_size_var = tk.IntVar()
        ttk.Spinbox(tab_frame, from_=1, to=8, width=5, textvariable=self.tab_size_var).pack(side=tk.LEFT, padx=5)
    
    def _create_appearance_tab(self):
        """创建外观选项卡"""
        appearance_frame = ttk.Frame(self.notebook)
        self.notebook.add(appearance_frame, text="外观")
        
        # 字体设置
        font_frame = ttk.LabelFrame(appearance_frame, text="字体")
        font_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 字体族
        font_family_frame = ttk.Frame(font_frame)
        font_family_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(font_family_frame, text="字体:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.font_family_var = tk.StringVar()
        font_family_combo = ttk.Combobox(font_family_frame, textvariable=self.font_family_var, width=20)
        font_family_combo["values"] = sorted(font.families())
        font_family_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 字体大小
        ttk.Label(font_family_frame, text="大小:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.font_size_var = tk.IntVar()
        ttk.Spinbox(font_family_frame, from_=8, to=72, width=5, textvariable=self.font_size_var).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # 字体粗细
        font_weight_frame = ttk.Frame(font_frame)
        font_weight_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(font_weight_frame, text="字形:").pack(side=tk.LEFT, padx=5)
        self.font_weight_var = tk.StringVar()
        weight_combo = ttk.Combobox(font_weight_frame, textvariable=self.font_weight_var, state="readonly", width=10)
        weight_combo["values"] = ("normal", "bold")
        weight_combo.pack(side=tk.LEFT, padx=5)
        
        # 字体预览
        preview_frame = ttk.LabelFrame(font_frame, text="预览")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.preview_text = tk.Text(preview_frame, height=3, width=40)
        self.preview_text.insert("1.0", "字体预览文本 AaBbCcDd 123456")
        self.preview_text.config(state="disabled")
        self.preview_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 字体变化事件绑定
        font_family_combo.bind("<<ComboboxSelected>>", self._update_font_preview)
        self.font_size_var.trace_add("write", lambda *args: self._update_font_preview())
        weight_combo.bind("<<ComboboxSelected>>", self._update_font_preview)
        
        # 颜色设置
        color_frame = ttk.LabelFrame(appearance_frame, text="颜色")
        color_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 前景色
        fg_frame = ttk.Frame(color_frame)
        fg_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(fg_frame, text="文本颜色:").pack(side=tk.LEFT, padx=5)
        self.fg_color_var = tk.StringVar()
        self.fg_color_entry = ttk.Entry(fg_frame, textvariable=self.fg_color_var, width=10)
        self.fg_color_entry.pack(side=tk.LEFT, padx=5)
        
        self.fg_color_button = ttk.Button(fg_frame, text="选择...", command=lambda: self._choose_color("foreground"))
        self.fg_color_button.pack(side=tk.LEFT, padx=5)
        
        self.fg_color_preview = tk.Canvas(fg_frame, width=20, height=20, bg="#000000")
        self.fg_color_preview.pack(side=tk.LEFT, padx=5)
        
        # 背景色
        bg_frame = ttk.Frame(color_frame)
        bg_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(bg_frame, text="背景颜色:").pack(side=tk.LEFT, padx=5)
        self.bg_color_var = tk.StringVar()
        self.bg_color_entry = ttk.Entry(bg_frame, textvariable=self.bg_color_var, width=10)
        self.bg_color_entry.pack(side=tk.LEFT, padx=5)
        
        self.bg_color_button = ttk.Button(bg_frame, text="选择...", command=lambda: self._choose_color("background"))
        self.bg_color_button.pack(side=tk.LEFT, padx=5)
        
        self.bg_color_preview = tk.Canvas(bg_frame, width=20, height=20, bg="#FFFFFF")
        self.bg_color_preview.pack(side=tk.LEFT, padx=5)
        
        # 选中文本颜色
        sel_frame = ttk.Frame(color_frame)
        sel_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(sel_frame, text="选中背景:").pack(side=tk.LEFT, padx=5)
        self.sel_color_var = tk.StringVar()
        self.sel_color_entry = ttk.Entry(sel_frame, textvariable=self.sel_color_var, width=10)
        self.sel_color_entry.pack(side=tk.LEFT, padx=5)
        
        self.sel_color_button = ttk.Button(sel_frame, text="选择...", command=lambda: self._choose_color("selection"))
        self.sel_color_button.pack(side=tk.LEFT, padx=5)
        
        self.sel_color_preview = tk.Canvas(sel_frame, width=20, height=20, bg="#B5D5FF")
        self.sel_color_preview.pack(side=tk.LEFT, padx=5)
        
        # 颜色变化事件绑定
        self.fg_color_var.trace_add("write", lambda *args: self._update_color_preview("foreground"))
        self.bg_color_var.trace_add("write", lambda *args: self._update_color_preview("background"))
        self.sel_color_var.trace_add("write", lambda *args: self._update_color_preview("selection"))
    
    def _create_file_tab(self):
        """创建文件选项卡"""
        file_frame = ttk.Frame(self.notebook)
        self.notebook.add(file_frame, text="文件")
        
        # 编码设置
        encoding_frame = ttk.LabelFrame(file_frame, text="编码")
        encoding_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(encoding_frame, text="默认编码:").pack(side=tk.LEFT, padx=10, pady=5)
        self.encoding_var = tk.StringVar()
        encoding_combo = ttk.Combobox(encoding_frame, textvariable=self.encoding_var, state="readonly", width=15)
        encoding_combo["values"] = ("UTF-8", "GB2312", "GBK", "UTF-16", "ASCII", "ISO-8859-1", "BIG5")
        encoding_combo.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 文件扩展名
        ext_frame = ttk.LabelFrame(file_frame, text="文件扩展名")
        ext_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(ext_frame, text="默认扩展名:").pack(side=tk.LEFT, padx=10, pady=5)
        self.ext_var = tk.StringVar()
        ext_combo = ttk.Combobox(ext_frame, textvariable=self.ext_var, width=10)
        ext_combo["values"] = (".txt", ".log", ".md", ".csv", ".json", ".xml", ".html")
        ext_combo.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 最近文件
        recent_frame = ttk.LabelFrame(file_frame, text="最近文件")
        recent_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(recent_frame, text="最大记录数:").pack(side=tk.LEFT, padx=10, pady=5)
        self.max_recent_var = tk.IntVar()
        ttk.Spinbox(recent_frame, from_=0, to=30, width=5, textvariable=self.max_recent_var).pack(side=tk.LEFT, padx=5, pady=5)
        
        ttk.Button(recent_frame, text="清除最近文件记录", 
                 command=self._clear_recent_files).pack(side=tk.LEFT, padx=10, pady=5)
    
    def _create_buttons(self):
        """创建底部按钮"""
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="确定", width=10, 
                 command=self._on_ok).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", width=10, 
                 command=self._on_cancel).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="应用", width=10, 
                 command=self._on_apply).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="重置为默认", width=12, 
                 command=self._on_reset).pack(side=tk.LEFT, padx=5)
    
    def _load_current_config(self):
        """加载当前配置"""
        # 常规选项
        window_config = self.config_manager.get("window")
        if window_config:
            self.start_maximized_var.set(window_config.get("startMaximized", False))
            self.remember_size_var.set(window_config.get("rememberSize", True))
            self.show_status_bar_var.set(window_config.get("showStatusBar", True))
            self.window_width_var.set(window_config.get("width", 800))
            self.window_height_var.set(window_config.get("height", 600))
        
        app_config = self.config_manager.get("app")
        if app_config:
            self.language_var.set(app_config.get("language", "zh-CN"))
        
        # 编辑器选项
        editor_config = self.config_manager.get("editor")
        if editor_config:
            behavior = editor_config.get("behavior", {})
            self.auto_indent_var.set(behavior.get("autoIndent", True))
            self.smart_indent_var.set(behavior.get("smartIndent", True))
            self.word_wrap_var.set(behavior.get("wordWrap", True))
            self.tab_size_var.set(behavior.get("tabSize", 4))
            
            font_config = editor_config.get("font", {})
            self.font_family_var.set(font_config.get("family", "Microsoft YaHei"))
            self.font_size_var.set(font_config.get("size", 12))
            self.font_weight_var.set(font_config.get("weight", "normal"))
            
            theme = editor_config.get("theme", {})
            self.fg_color_var.set(theme.get("foreground", "#000000"))
            self.bg_color_var.set(theme.get("background", "#FFFFFF"))
            self.sel_color_var.set(theme.get("selection", "#B5D5FF"))
        
        # 文件选项
        file_config = self.config_manager.get("file")
        if file_config:
            self.encoding_var.set(file_config.get("encoding", "UTF-8"))
            self.ext_var.set(file_config.get("defaultExtension", ".txt"))
            self.max_recent_var.set(file_config.get("maxRecentFiles", 10))
        
        # 更新预览
        self._update_font_preview()
        self._update_color_preview("foreground")
        self._update_color_preview("background")
        self._update_color_preview("selection")
    
    def _update_font_preview(self, event=None):
        """更新字体预览"""
        try:
            family = self.font_family_var.get()
            size = self.font_size_var.get()
            weight = self.font_weight_var.get()
            
            if not family or not size:
                return
            
            preview_font = (family, size, weight)
            self.preview_text.config(state="normal")
            self.preview_text.config(font=preview_font)
            self.preview_text.config(state="disabled")
        except:
            pass
    
    def _update_color_preview(self, color_type):
        """更新颜色预览"""
        try:
            if color_type == "foreground":
                color = self.fg_color_var.get()
                self.fg_color_preview.config(bg=color)
                self.preview_text.config(fg=color)
            elif color_type == "background":
                color = self.bg_color_var.get()
                self.bg_color_preview.config(bg=color)
                self.preview_text.config(bg=color)
            elif color_type == "selection":
                color = self.sel_color_var.get()
                self.sel_color_preview.config(bg=color)
        except:
            pass
    
    def _choose_color(self, color_type):
        """颜色选择对话框"""
        current_color = ""
        if color_type == "foreground":
            current_color = self.fg_color_var.get()
        elif color_type == "background":
            current_color = self.bg_color_var.get()
        elif color_type == "selection":
            current_color = self.sel_color_var.get()
        
        color = colorchooser.askcolor(color=current_color, title="选择颜色")
        if color and color[1]:  # color[1] 是十六进制颜色值
            if color_type == "foreground":
                self.fg_color_var.set(color[1])
            elif color_type == "background":
                self.bg_color_var.set(color[1])
            elif color_type == "selection":
                self.sel_color_var.set(color[1])
    
    def _clear_recent_files(self):
        """清除最近文件记录"""
        self.config_manager.set("file", "recentFiles", [])
        tk.messagebox.showinfo("提示", "最近文件记录已清除", parent=self.dialog)
    
    def _save_config(self):
        """保存配置"""
        # 常规选项
        self.config_manager.set("window", "startMaximized", self.start_maximized_var.get())
        self.config_manager.set("window", "rememberSize", self.remember_size_var.get())
        self.config_manager.set("window", "showStatusBar", self.show_status_bar_var.get())
        self.config_manager.set("window", "width", self.window_width_var.get())
        self.config_manager.set("window", "height", self.window_height_var.get())
        
        self.config_manager.set("app", "language", self.language_var.get())
        
        # 编辑器选项
        self.config_manager.set("editor", "behavior", {
            "autoIndent": self.auto_indent_var.get(),
            "smartIndent": self.smart_indent_var.get(),
            "wordWrap": self.word_wrap_var.get(),
            "tabSize": self.tab_size_var.get()
        })
        
        self.config_manager.set("editor", "font", {
            "family": self.font_family_var.get(),
            "size": self.font_size_var.get(),
            "weight": self.font_weight_var.get()
        })
        
        self.config_manager.set("editor", "theme", {
            "foreground": self.fg_color_var.get(),
            "background": self.bg_color_var.get(),
            "selection": self.sel_color_var.get()
        })
        
        # 文件选项
        self.config_manager.set("file", "encoding", self.encoding_var.get())
        self.config_manager.set("file", "defaultExtension", self.ext_var.get())
        self.config_manager.set("file", "maxRecentFiles", self.max_recent_var.get())