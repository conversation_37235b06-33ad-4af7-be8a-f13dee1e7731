import re
import datetime
import tkinter as tk
from tkinter import font as tkfont
from collections import deque
from utils.config import ConfigManager

class Editor:
    """
    编辑器核心类，负责文本编辑、撤销/重做、查找替换等功能
    """
    def __init__(self, config_manager=None):
        """
        初始化编辑器
        
        Args:
            config_manager: 配置管理器实例，如果为None则创建新实例
        """
        # 初始化配置管理器
        if config_manager is None:
            self.config_manager = ConfigManager()
        else:
            self.config_manager = config_manager
            
        # 文本内容
        self.content = ""
        
        # 光标位置 (行, 列)
        self.cursor_position = (0, 0)
        
        # 选择区域 (起始位置, 结束位置)
        self.selection = None
        
        # 撤销/重做栈
        self.undo_stack = deque(maxlen=100)  # 最多保存100步操作
        self.redo_stack = deque(maxlen=100)
        
        # 查找替换相关
        self.last_search = ""
        self.last_replace = ""
        self.search_case_sensitive = False
        self.search_whole_word = False
        self.search_use_regex = False
        
        # 字体设置
        self.font_family = "宋体"
        self.font_size = 12
        self.font_weight = "normal"  # normal, bold
        self.font_slant = "roman"    # roman, italic
        self.font_underline = False
        self.font_overstrike = False
        
        # 加载编辑器配置
        self._load_config()
    
    def _load_config(self):
        """
        加载编辑器配置
        """
        editor_config = self.config_manager.get('editor')
        if editor_config:
            # 设置编辑器行为
            behavior = editor_config.get('behavior', {})
            self.auto_indent = behavior.get('autoIndent', True)
            self.smart_indent = behavior.get('smartIndent', True)
            self.tab_size = behavior.get('tabSize', 4)
            self.word_wrap = behavior.get('wordWrap', True)
    
    def _save_state(self):
        """
        保存当前状态到撤销栈
        """
        state = {
            'content': self.content,
            'cursor_position': self.cursor_position,
            'selection': self.selection
        }
        self.undo_stack.append(state)
    
    def _position_to_index(self, position):
        """
        将(行,列)位置转换为文本索引
        
        Args:
            position: 位置 (行, 列)
            
        Returns:
            int: 文本索引
        """
        lines = self.content.split('\n')
        index = 0
        
        # 计算前面行的长度
        for i in range(min(position[0], len(lines))):
            index += len(lines[i]) + 1  # +1 是换行符
        
        # 加上当前行的列数
        if position[0] < len(lines):
            index += min(position[1], len(lines[position[0]]))
        
        return index
    
    def _index_to_position(self, index):
        """
        将文本索引转换为(行,列)位置
        
        Args:
            index: 文本索引
            
        Returns:
            tuple: 位置 (行, 列)
        """
        if index <= 0:
            return (0, 0)
        
        if index >= len(self.content):
            # 如果索引超出内容长度，返回最后位置
            lines = self.content.split('\n')
            return (len(lines) - 1, len(lines[-1]))
        
        # 计算行号和列号
        text_before = self.content[:index]
        lines = text_before.split('\n')
        line = len(lines) - 1
        col = len(lines[-1])
        
        return (line, col)
    
    def set_content(self, content):
        """
        设置编辑器内容
        
        Args:
            content: 新的文本内容
        """
        # 保存当前状态用于撤销
        self._save_state()
        
        # 更新内容
        self.content = content
        
        # 重置光标位置
        self.cursor_position = (0, 0)
        self.selection = None
        
        # 清空重做栈
        self.redo_stack.clear()
    
    def get_content(self):
        """
        获取编辑器内容
        
        Returns:
            str: 当前文本内容
        """
        return self.content
    
    def insert_text(self, text, position=None):
        """
        在指定位置插入文本
        
        Args:
            text: 要插入的文本
            position: 插入位置 (行, 列)，如果为None则使用当前光标位置
            
        Returns:
            tuple: 插入后的新光标位置 (行, 列)
        """
        # 保存当前状态用于撤销
        self._save_state()
        
        # 如果有选中区域，先删除选中内容
        if self.selection:
            self.delete_selected_text()
        
        # 确定插入位置
        if position is None:
            position = self.cursor_position
        
        # 将位置转换为索引
        index = self._position_to_index(position)
        
        # 插入文本
        new_content = self.content[:index] + text + self.content[index:]
        self.content = new_content
        
        # 更新光标位置
        lines = text.split('\n')
        if len(lines) > 1:
            # 多行插入，计算新的行和列
            last_line = lines[-1]
            new_line = position[0] + len(lines) - 1
            new_col = len(last_line)
        else:
            # 单行插入，只更新列
            new_line = position[0]
            new_col = position[1] + len(text)
        
        new_position = (new_line, new_col)
        self.cursor_position = new_position
        
        # 清空重做栈
        self.redo_stack.clear()
        
        return new_position
    
    def delete_text(self, start_position, end_position):
        """
        删除指定范围的文本
        
        Args:
            start_position: 起始位置 (行, 列)
            end_position: 结束位置 (行, 列)
            
        Returns:
            tuple: 删除后的新光标位置 (行, 列)
        """
        # 保存当前状态用于撤销
        self._save_state()
        
        # 将位置转换为索引
        start_index = self._position_to_index(start_position)
        end_index = self._position_to_index(end_position)
        
        # 确保起始索引小于结束索引
        if start_index > end_index:
            start_index, end_index = end_index, start_index
        
        # 删除文本
        new_content = self.content[:start_index] + self.content[end_index:]
        self.content = new_content
        
        # 更新光标位置
        self.cursor_position = start_position
        self.selection = None
        
        # 清空重做栈
        self.redo_stack.clear()
        
        return start_position
    
    def delete_selected_text(self):
        """
        删除选中的文本
        
        Returns:
            tuple: 删除后的新光标位置 (行, 列)
        """
        if not self.selection:
            return self.cursor_position
        
        return self.delete_text(self.selection[0], self.selection[1])
    
    def set_cursor_position(self, position):
        """
        设置光标位置
        
        Args:
            position: 新的光标位置 (行, 列)
        """
        # 确保位置有效
        lines = self.content.split('\n')
        max_line = max(0, len(lines) - 1)
        line = min(max(0, position[0]), max_line)
        
        if line < len(lines):
            max_col = len(lines[line])
            col = min(max(0, position[1]), max_col)
        else:
            col = 0
        
        self.cursor_position = (line, col)
        
        # 清除选择区域
        self.selection = None
    
    def get_cursor_position(self):
        """
        获取当前光标位置
        
        Returns:
            tuple: 当前光标位置 (行, 列)
        """
        return self.cursor_position
    
    def set_selection(self, start_position, end_position):
        """
        设置选择区域
        
        Args:
            start_position: 起始位置 (行, 列)
            end_position: 结束位置 (行, 列)
        """
        self.selection = (start_position, end_position)
        self.cursor_position = end_position
    
    def get_selection(self):
        """
        获取当前选择区域
        
        Returns:
            tuple: 选择区域 (起始位置, 结束位置)，如果没有选择则返回None
        """
        return self.selection
    
    def get_selected_text(self):
        """
        获取选中的文本
        
        Returns:
            str: 选中的文本，如果没有选择则返回空字符串
        """
        if not self.selection:
            return ""
        
        start_index = self._position_to_index(self.selection[0])
        end_index = self._position_to_index(self.selection[1])
        
        # 确保起始索引小于结束索引
        if start_index > end_index:
            start_index, end_index = end_index, start_index
        
        return self.content[start_index:end_index]
    
    def undo(self):
        """
        撤销上一步操作
        
        Returns:
            bool: 是否成功撤销
        """
        if not self.undo_stack:
            return False
        
        # 保存当前状态到重做栈
        current_state = {
            'content': self.content,
            'cursor_position': self.cursor_position,
            'selection': self.selection
        }
        self.redo_stack.append(current_state)
        
        # 恢复上一个状态
        previous_state = self.undo_stack.pop()
        self.content = previous_state['content']
        self.cursor_position = previous_state['cursor_position']
        self.selection = previous_state['selection']
        
        return True
    
    def redo(self):
        """
        重做上一步撤销的操作
        
        Returns:
            bool: 是否成功重做
        """
        if not self.redo_stack:
            return False
        
        # 保存当前状态到撤销栈
        current_state = {
            'content': self.content,
            'cursor_position': self.cursor_position,
            'selection': self.selection
        }
        self.undo_stack.append(current_state)
        
        # 恢复下一个状态
        next_state = self.redo_stack.pop()
        self.content = next_state['content']
        self.cursor_position = next_state['cursor_position']
        self.selection = next_state['selection']
        
        return True
    
    def find_text(self, search_text, start_position=None, case_sensitive=None, whole_word=None, use_regex=None):
        """
        查找文本
        
        Args:
            search_text: 要查找的文本
            start_position: 开始查找的位置 (行, 列)，如果为None则从当前光标位置开始
            case_sensitive: 是否区分大小写，如果为None则使用上次设置
            whole_word: 是否全字匹配，如果为None则使用上次设置
            use_regex: 是否使用正则表达式，如果为None则使用上次设置
            
        Returns:
            tuple: 查找结果 (起始位置, 结束位置)，如果未找到则返回None
        """
        if not search_text:
            return None
        
        # 更新查找设置
        self.last_search = search_text
        if case_sensitive is not None:
            self.search_case_sensitive = case_sensitive
        if whole_word is not None:
            self.search_whole_word = whole_word
        if use_regex is not None:
            self.search_use_regex = use_regex
        
        # 确定开始位置
        if start_position is None:
            start_position = self.cursor_position
        
        start_index = self._position_to_index(start_position)
        
        # 准备查找
        content = self.content
        search_pattern = search_text
        
        # 处理查找选项
        if self.search_use_regex:
            # 使用正则表达式
            if self.search_whole_word:
                search_pattern = r'\b' + search_pattern + r'\b'
            
            try:
                if self.search_case_sensitive:
                    pattern = re.compile(search_pattern)
                else:
                    pattern = re.compile(search_pattern, re.IGNORECASE)
                
                # 在指定位置之后查找
                match = pattern.search(content, start_index)
                if match:
                    start_idx = match.start()
                    end_idx = match.end()
                    return (self._index_to_position(start_idx), self._index_to_position(end_idx))
            except re.error:
                # 正则表达式错误
                return None
        else:
            # 普通文本查找
            if not self.search_case_sensitive:
                content = content.lower()
                search_pattern = search_pattern.lower()
            
            # 全字匹配
            if self.search_whole_word:
                # 构建正则表达式进行全字匹配
                pattern = re.compile(r'\b' + re.escape(search_pattern) + r'\b')
                if not self.search_case_sensitive:
                    pattern = re.compile(r'\b' + re.escape(search_pattern) + r'\b', re.IGNORECASE)
                
                # 在指定位置之后查找
                match = pattern.search(content, start_index)
                if match:
                    start_idx = match.start()
                    end_idx = match.end()
                    return (self._index_to_position(start_idx), self._index_to_position(end_idx))
            else:
                # 普通文本查找
                if self.search_case_sensitive:
                    idx = content.find(search_pattern, start_index)
                else:
                    idx = content.lower().find(search_pattern.lower(), start_index)
                
                if idx != -1:
                    start_idx = idx
                    end_idx = idx + len(search_pattern)
                    return (self._index_to_position(start_idx), self._index_to_position(end_idx))
        
        return None
    
    def replace_text(self, search_text, replace_text, start_position=None, case_sensitive=None, whole_word=None, use_regex=None):
        """
        查找并替换文本
        
        Args:
            search_text: 要查找的文本
            replace_text: 替换的文本
            start_position: 开始查找的位置 (行, 列)，如果为None则从当前光标位置开始
            case_sensitive: 是否区分大小写，如果为None则使用上次设置
            whole_word: 是否全字匹配，如果为None则使用上次设置
            use_regex: 是否使用正则表达式，如果为None则使用上次设置
            
        Returns:
            tuple: 替换后的新光标位置 (行, 列)，如果未找到则返回None
        """
        # 更新替换文本
        self.last_replace = replace_text
        
        # 查找文本
        found = self.find_text(search_text, start_position, case_sensitive, whole_word, use_regex)
        if not found:
            return None
        
        # 替换文本
        start_pos, end_pos = found
        self.delete_text(start_pos, end_pos)
        new_pos = self.insert_text(replace_text, start_pos)
        
        return new_pos
    
    def replace_all_text(self, search_text, replace_text, case_sensitive=None, whole_word=None, use_regex=None):
        """
        替换所有匹配的文本
        
        Args:
            search_text: 要查找的文本
            replace_text: 替换的文本
            case_sensitive: 是否区分大小写，如果为None则使用上次设置
            whole_word: 是否全字匹配，如果为None则使用上次设置
            use_regex: 是否使用正则表达式，如果为None则使用上次设置
            
        Returns:
            int: 替换的次数
        """
        if not search_text:
            return 0
        
        # 保存当前状态用于撤销
        self._save_state()
        
        # 更新替换文本
        self.last_replace = replace_text
        
        # 保存当前光标位置
        original_position = self.cursor_position
        
        # 从头开始查找
        count = 0
        self.cursor_position = (0, 0)
        
        while True:
            found = self.find_text(search_text, self.cursor_position, case_sensitive, whole_word, use_regex)
            if not found:
                break
            
            # 替换文本
            start_pos, end_pos = found
            self.delete_text(start_pos, end_pos)
            new_pos = self.insert_text(replace_text, start_pos)
            
            # 更新光标位置
            self.cursor_position = new_pos
            
            count += 1
        
        # 恢复光标位置
        self.cursor_position = original_position
        
        return count
    
    def get_line_count(self):
        """
        获取文本的行数
        
        Returns:
            int: 行数
        """
        return len(self.content.split('\n'))
    
    def get_line(self, line_number):
        """
        获取指定行的文本
        
        Args:
            line_number: 行号 (从0开始)
            
        Returns:
            str: 行文本，如果行号无效则返回空字符串
        """
        lines = self.content.split('\n')
        if 0 <= line_number < len(lines):
            return lines[line_number]
        return ""
    
    def set_line(self, line_number, text):
        """
        设置指定行的文本
        
        Args:
            line_number: 行号 (从0开始)
            text: 新的行文本
            
        Returns:
            bool: 是否成功设置
        """
        lines = self.content.split('\n')
        if 0 <= line_number < len(lines):
            # 保存当前状态用于撤销
            self._save_state()
            
            # 更新行文本
            lines[line_number] = text
            self.content = '\n'.join(lines)
            
            # 清空重做栈
            self.redo_stack.clear()
            
            return True
        return False
    
    def insert_line(self, line_number, text):
        """
        在指定行前插入新行
        
        Args:
            line_number: 行号 (从0开始)
            text: 新行的文本
            
        Returns:
            bool: 是否成功插入
        """
        lines = self.content.split('\n')
        if 0 <= line_number <= len(lines):
            # 保存当前状态用于撤销
            self._save_state()
            
            # 插入新行
            lines.insert(line_number, text)
            self.content = '\n'.join(lines)
            
            # 清空重做栈
            self.redo_stack.clear()
            
            return True
        return False
    
    def delete_line(self, line_number):
        """
        删除指定行
        
        Args:
            line_number: 行号 (从0开始)
            
        Returns:
            bool: 是否成功删除
        """
        lines = self.content.split('\n')
        if 0 <= line_number < len(lines):
            # 保存当前状态用于撤销
            self._save_state()
            
            # 删除行
            del lines[line_number]
            self.content = '\n'.join(lines)
            
            # 更新光标位置
            if self.cursor_position[0] >= line_number:
                new_line = max(0, self.cursor_position[0] - 1)
                new_col = min(self.cursor_position[1], len(lines[new_line]) if new_line < len(lines) else 0)
                self.cursor_position = (new_line, new_col)
            
            # 清空重做栈
            self.redo_stack.clear()
            
            return True
        return False
    
    def get_word_at_position(self, position=None):
        """
        获取指定位置的单词
        
        Args:
            position: 位置 (行, 列)，如果为None则使用当前光标位置
            
        Returns:
            tuple: (单词, 单词起始位置, 单词结束位置)，如果位置无效则返回(None, None, None)
        """
        if position is None:
            position = self.cursor_position
            
        line_number = position[0]
        col = position[1]
        
        # 获取当前行文本
        line = self.get_line(line_number)
        if not line:
            return (None, None, None)
        
        # 查找单词边界
        # 向左查找单词开始
        start = col
        while start > 0 and re.match(r'\w', line[start-1]):
            start -= 1
            
        # 向右查找单词结束
        end = col
        while end < len(line) and re.match(r'\w', line[end]):
            end += 1
            
        if start == end:
            return (None, None, None)
            
        word = line[start:end]
        return (word, (line_number, start), (line_number, end))
    
    def auto_indent(self, position=None):
        """
        自动缩进功能，在换行时保持与上一行相同的缩进
        
        Args:
            position: 位置 (行, 列)，如果为None则使用当前光标位置
            
        Returns:
            str: 缩进字符串
        """
        if not self.auto_indent:
            return ""
            
        if position is None:
            position = self.cursor_position
            
        line_number = position[0]
        
        # 获取上一行
        if line_number <= 0:
            return ""
            
        prev_line = self.get_line(line_number - 1)
        
        # 计算缩进
        indent = ""
        for char in prev_line:
            if char in (' ', '\t'):
                indent += char
            else:
                break
                
        # 智能缩进：如果上一行以冒号结尾，增加一级缩进
        if self.smart_indent and prev_line.rstrip().endswith(':'):
            indent += ' ' * self.tab_size
            
        return indent
    
    def cut_text(self, text_widget):
        """
        剪切所选文本到剪贴板
        
        Args:
            text_widget: 文本控件
        """
        if text_widget.tag_ranges("sel"):
            text_widget.event_generate("<<Cut>>")
    
    def copy_text(self, text_widget):
        """
        复制所选文本到剪贴板
        
        Args:
            text_widget: 文本控件
        """
        try:
            if text_widget.tag_ranges("sel"):
                # 获取选中的文本
                selected_text = text_widget.get("sel.first", "sel.last")
                # 清除剪贴板
                text_widget.clipboard_clear()
                # 将文本添加到剪贴板
                text_widget.clipboard_append(selected_text)
        except tk.TclError:
            pass
    
    def paste_text(self, text_widget):
        """
        从剪贴板粘贴文本
        
        Args:
            text_widget: 文本控件
        """
        try:
            # 获取剪贴板内容
            clipboard_text = text_widget.clipboard_get()
            # 如果有选中文本，先删除
            if text_widget.tag_ranges("sel"):
                text_widget.delete("sel.first", "sel.last")
            # 在光标位置插入文本
            text_widget.insert("insert", clipboard_text)
        except tk.TclError:
            pass
    
    def delete_text(self, text_widget):
        """
        删除所选文本
        
        Args:
            text_widget: 文本控件
        """
        if text_widget.tag_ranges("sel"):
            text_widget.delete("sel.first", "sel.last")
    
    def select_all(self, text_widget):
        """
        选择全部文本
        
        Args:
            text_widget: 文本控件
        """
        text_widget.tag_add("sel", "1.0", "end")
    
    def insert_datetime(self, text_widget):
        """
        在当前位置插入日期和时间
        
        Args:
            text_widget: 文本控件
        """
        now = datetime.datetime.now()
        datetime_str = now.strftime("%H:%M %Y-%m-%d")
        text_widget.insert("insert", datetime_str)
    
    def choose_font(self, parent):
        """
        打开字体选择对话框
        
        Args:
            parent: 父窗口

        Returns:
            tuple 或 None: 返回选择的字体信息，如果用户取消则返回None
        """
        # 获取当前配置的字体信息
        current_font = self.config_manager.get('editor', {}).get('font', {})
        
        # 从配置中获取字体参数，如果没有则使用默认值
        font_family = current_font.get('family', self.font_family)
        font_size = current_font.get('size', self.font_size)
        font_weight = current_font.get('weight', self.font_weight)
        font_slant = current_font.get('slant', self.font_slant)
        font_underline = current_font.get('underline', self.font_underline)
        font_overstrike = current_font.get('overstrike', self.font_overstrike)
        
        # 创建字体选择对话框
        dialog = tk.Toplevel(parent)
        dialog.title("选择字体")
        dialog.geometry("500x450")
        dialog.resizable(False, False)
        dialog.transient(parent)
        dialog.grab_set()
        
        # 对话框结果
        result = {
            "ok": False,
            "family": font_family,
            "size": font_size,
            "weight": font_weight,
            "slant": font_slant,
            "underline": font_underline,
            "overstrike": font_overstrike
        }
        
        # 创建界面元素
        # 字体选择框架
        font_frame = tk.Frame(dialog)
        font_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧 - 字体家族选择
        left_frame = tk.Frame(font_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        tk.Label(left_frame, text="字体:").pack(anchor=tk.W)
        
        # 创建字体选择列表框
        fonts_listbox = tk.Listbox(left_frame, exportselection=False)
        fonts_listbox.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        fonts_scrollbar = tk.Scrollbar(fonts_listbox, orient=tk.VERTICAL, command=fonts_listbox.yview)
        fonts_listbox.configure(yscrollcommand=fonts_scrollbar.set)
        fonts_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 获取系统中所有可用字体
        font_families = sorted(list(tkfont.families()))
        for font_name in font_families:
            fonts_listbox.insert(tk.END, font_name)
        
        # 选择当前字体
        if font_family in font_families:
            idx = font_families.index(font_family)
            fonts_listbox.select_set(idx)
            fonts_listbox.see(idx)
        
        # 中间 - 字体样式选择
        middle_frame = tk.Frame(font_frame)
        middle_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        tk.Label(middle_frame, text="字体样式:").pack(anchor=tk.W)
        
        # 创建样式选择列表框
        styles_listbox = tk.Listbox(middle_frame, exportselection=False, height=4)
        styles_listbox.pack(fill=tk.X, expand=False)
        
        # 字体样式选项
        styles = [
            ("常规", "normal", "roman"),
            ("粗体", "bold", "roman"),
            ("斜体", "normal", "italic"),
            ("粗斜体", "bold", "italic")
        ]
        
        for style_name, weight, slant in styles:
            styles_listbox.insert(tk.END, style_name)
        
        # 选择当前样式
        current_style_idx = 0
        for i, (_, w, s) in enumerate(styles):
            if w == font_weight and s == font_slant:
                current_style_idx = i
                break
        styles_listbox.select_set(current_style_idx)
        
        # 添加下划线和删除线选项
        tk.Label(middle_frame, text="效果:").pack(anchor=tk.W, pady=(10, 0))
        
        # 下划线复选框
        underline_var = tk.BooleanVar()
        underline_var.set(font_underline)
        underline_cb = tk.Checkbutton(middle_frame, text="下划线", variable=underline_var)
        underline_cb.pack(anchor=tk.W)
        
        # 删除线复选框
        overstrike_var = tk.BooleanVar()
        overstrike_var.set(font_overstrike)
        overstrike_cb = tk.Checkbutton(middle_frame, text="删除线", variable=overstrike_var)
        overstrike_cb.pack(anchor=tk.W)
        
        # 右侧 - 字体大小选择
        right_frame = tk.Frame(font_frame)
        right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        tk.Label(right_frame, text="大小:").pack(anchor=tk.W)
        
        # 创建大小选择列表框
        sizes_listbox = tk.Listbox(right_frame, exportselection=False)
        sizes_listbox.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        sizes_scrollbar = tk.Scrollbar(sizes_listbox, orient=tk.VERTICAL, command=sizes_listbox.yview)
        sizes_listbox.configure(yscrollcommand=sizes_scrollbar.set)
        sizes_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 常用字体大小
        common_sizes = [8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72]
        for size in common_sizes:
            sizes_listbox.insert(tk.END, str(size))
        
        # 选择当前大小
        if font_size in common_sizes:
            idx = common_sizes.index(font_size)
            sizes_listbox.select_set(idx)
            sizes_listbox.see(idx)
        
        # 预览框架
        preview_frame = tk.Frame(dialog, height=100, bd=1, relief=tk.SUNKEN)
        preview_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 预览标签
        tk.Label(preview_frame, text="预览:").pack(anchor=tk.W, padx=5, pady=5)
        
        # 预览文本
        preview_text = tk.Text(preview_frame, height=3, width=40, wrap=tk.WORD)
        preview_text.pack(padx=5, pady=5, fill=tk.BOTH, expand=True)
        preview_text.insert("1.0", "中文字体预览 AaBbYyZz")
        preview_text.config(state=tk.DISABLED)
        
        # 更新预览函数
        def update_preview():
            # 获取当前选择
            try:
                family = font_families[fonts_listbox.curselection()[0]]
                style_idx = styles_listbox.curselection()[0]
                _, weight, slant = styles[style_idx]
                size = common_sizes[sizes_listbox.curselection()[0]]
                underline = underline_var.get()
                overstrike = overstrike_var.get()
                
                # 创建预览字体
                preview_font = tkfont.Font(family=family, size=size, weight=weight,
                                         slant=slant, underline=underline, overstrike=overstrike)
                
                # 更新预览
                preview_text.config(state=tk.NORMAL)
                preview_text.delete("1.0", tk.END)
                preview_text.insert("1.0", "中文字体预览 AaBbYyZz")
                preview_text.config(font=preview_font, state=tk.DISABLED)
                
                # 更新结果
                result["family"] = family
                result["size"] = size
                result["weight"] = weight
                result["slant"] = slant
                result["underline"] = underline
                result["overstrike"] = overstrike
            except (IndexError, TclError):
                pass
        
        # 绑定选择事件
        fonts_listbox.bind("<<ListboxSelect>>", lambda e: update_preview())
        styles_listbox.bind("<<ListboxSelect>>", lambda e: update_preview())
        sizes_listbox.bind("<<ListboxSelect>>", lambda e: update_preview())
        underline_cb.config(command=update_preview)
        overstrike_cb.config(command=update_preview)
        
        # 初始更新预览
        update_preview()
        
        # 底部按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)
        
        def on_ok():
            result["ok"] = True
            dialog.destroy()
        
        def on_cancel():
            dialog.destroy()
        
        # 创建按钮
        tk.Button(button_frame, text="确定", width=10, command=on_ok).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="取消", width=10, command=on_cancel).pack(side=tk.LEFT, padx=5)
        
        # 等待对话框关闭
        parent.wait_window(dialog)
        
        # 返回结果
        if result["ok"]:
            # 保存字体配置
            font_config = {
                "family": result["family"],
                "size": result["size"],
                "weight": result["weight"],
                "slant": result["slant"],
                "underline": result["underline"],
                "overstrike": result["overstrike"]
            }
            
            # 更新编辑器配置
            self.font_family = result["family"]
            self.font_size = result["size"]
            self.font_weight = result["weight"]
            self.font_slant = result["slant"]
            self.font_underline = result["underline"]
            self.font_overstrike = result["overstrike"]
            
            # 更新配置管理器
            editor_config = self.config_manager.get('editor', {})
            editor_config['font'] = font_config
            self.config_manager.set('editor', editor_config)
            self.config_manager.save()
            
            # 创建字体元组，包含完整的字体配置
            font_tuple = (result["family"], result["size"], 
                          result["weight"], result["slant"], 
                          result["underline"], result["overstrike"])
            
            return font_tuple
        
        return None
        
    def apply_config_to_text_widget(self, text_widget):
        """
        将编辑器配置应用到文本控件
        
        Args:
            text_widget: 文本控件
        """
        # 设置字体
        font_tuple = (self.font_family, self.font_size, self.font_weight, 
                      self.font_slant, self.font_underline, self.font_overstrike)
        text_widget.config(font=font_tuple)
        
        # 设置自动换行
        text_widget.config(wrap=tk.WORD if self.config_manager.get('editor.behavior.wordWrap', True) else tk.NONE)
    
    def find_text(self, text_widget, parent_window):
        """
        显示查找对话框
        
        Args:
            text_widget: 文本控件
            parent_window: 父窗口
        """
        # 创建查找对话框
        find_dialog = tk.Toplevel(parent_window)
        find_dialog.title("查找")
        find_dialog.geometry("300x150")
        find_dialog.resizable(False, False)
        find_dialog.transient(parent_window)
        find_dialog.grab_set()
        
        # 创建查找框架
        find_frame = tk.Frame(find_dialog)
        find_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加查找文本输入框
        tk.Label(find_frame, text="查找内容:").grid(row=0, column=0, sticky="w", pady=5)
        search_text = tk.Entry(find_frame, width=30)
        search_text.grid(row=0, column=1, sticky="ew", pady=5)
        
        # 如果有选中文本，则填入输入框
        try:
            selected_text = text_widget.get(tk.SEL_FIRST, tk.SEL_LAST)
            search_text.insert(0, selected_text)
        except tk.TclError:
            # 没有选中文本，尝试使用上次的搜索文本
            if self.last_search:
                search_text.insert(0, self.last_search)
        
        search_text.select_range(0, tk.END)
        search_text.focus_set()
        
        # 添加搜索选项
        options_frame = tk.Frame(find_frame)
        options_frame.grid(row=1, column=0, columnspan=2, sticky="w", pady=5)
        
        case_sensitive_var = tk.BooleanVar(value=self.search_case_sensitive)
        whole_word_var = tk.BooleanVar(value=self.search_whole_word)
        use_regex_var = tk.BooleanVar(value=self.search_use_regex)
        
        tk.Checkbutton(options_frame, text="区分大小写", variable=case_sensitive_var).pack(side=tk.LEFT, padx=5)
        tk.Checkbutton(options_frame, text="全字匹配", variable=whole_word_var).pack(side=tk.LEFT, padx=5)
        tk.Checkbutton(options_frame, text="使用正则表达式", variable=use_regex_var).pack(side=tk.LEFT, padx=5)
        
        # 添加按钮
        button_frame = tk.Frame(find_dialog)
        button_frame.pack(pady=10)
        
        # 当前位置变量，用于记录搜索位置
        current_pos = [None]
        
        # 查找下一个
        def find_next():
            search_pattern = search_text.get()
            if not search_pattern:
                return
            
            # 更新搜索设置
            self.last_search = search_pattern
            self.search_case_sensitive = case_sensitive_var.get()
            self.search_whole_word = whole_word_var.get()
            self.search_use_regex = use_regex_var.get()
            
            # 确定开始位置
            if current_pos[0] is None:
                try:
                    # 如果有选中文本，从选中结束位置开始
                    start_pos = text_widget.index(tk.SEL_LAST)
                except tk.TclError:
                    # 否则从光标位置开始
                    start_pos = text_widget.index(tk.INSERT)
            else:
                # 使用上次搜索的结束位置
                start_pos = current_pos[0]
            
            # 执行搜索
            if self.search_use_regex:
                try:
                    pattern_text = search_pattern
                    if self.search_whole_word:
                        pattern_text = r'\b' + pattern_text + r'\b'
                    
                    pattern = re.compile(pattern_text, 0 if self.search_case_sensitive else re.IGNORECASE)
                    
                    # 获取剩余文本
                    remaining_text = text_widget.get(start_pos, tk.END)
                    match = pattern.search(remaining_text)
                    
                    if match:
                        start_idx = match.start()
                        end_idx = match.end()
                        
                        # 计算实际位置
                        match_start = f"{start_pos}+{start_idx}c"
                        match_end = f"{start_pos}+{end_idx}c"
                        
                        # 选中匹配文本
                        text_widget.tag_remove(tk.SEL, "1.0", tk.END)
                        text_widget.tag_add(tk.SEL, match_start, match_end)
                        text_widget.mark_set(tk.INSERT, match_end)
                        text_widget.see(match_start)
                        
                        # 更新位置
                        current_pos[0] = match_end
                        return True
                except re.error:
                    tk.messagebox.showerror("错误", "无效的正则表达式")
                    return False
            else:
                # 普通文本搜索
                search_args = {
                    "stopindex": tk.END
                }
                
                if not self.search_case_sensitive:
                    search_args["nocase"] = 1
                
                if self.search_whole_word:
                    # 全字匹配
                    search_args["regexp"] = True
                    search_pattern = r'\b' + re.escape(search_pattern) + r'\b'
                
                # 执行搜索
                pos = text_widget.search(search_pattern, start_pos, **search_args)
                
                if pos:
                    # 计算结束位置
                    if self.search_whole_word:
                        # 如果使用正则表达式，需要特殊处理结束位置
                        search_len = len(search_text.get())
                        end_pos = f"{pos}+{search_len}c"
                    else:
                        end_pos = f"{pos}+{len(search_pattern)}c"
                    
                    # 选中匹配文本
                    text_widget.tag_remove(tk.SEL, "1.0", tk.END)
                    text_widget.tag_add(tk.SEL, pos, end_pos)
                    text_widget.mark_set(tk.INSERT, end_pos)
                    text_widget.see(pos)
                    
                    # 更新位置
                    current_pos[0] = end_pos
                    return True
            
            # 如果没有找到匹配，从头开始搜索
            current_pos[0] = "1.0"
            if start_pos != "1.0":
                if tk.messagebox.askyesno("查找", "已到达文件末尾，是否从头开始搜索？"):
                    return find_next()
            else:
                tk.messagebox.showinfo("查找", "未找到匹配项")
            
            return False
        
        # 添加按钮
        tk.Button(button_frame, text="查找下一个", width=15, command=find_next).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="取消", width=15, command=find_dialog.destroy).pack(side=tk.LEFT, padx=5)
        
        # 绑定回车键
        search_text.bind("<Return>", lambda e: find_next())
        find_dialog.bind("<Escape>", lambda e: find_dialog.destroy())
    
    def replace_text(self, text_widget, parent_window):
        """
        显示替换对话框
        
        Args:
            text_widget: 文本控件
            parent_window: 父窗口
        """
        # 创建替换对话框
        replace_dialog = tk.Toplevel(parent_window)
        replace_dialog.title("替换")
        replace_dialog.geometry("350x200")
        replace_dialog.resizable(False, False)
        replace_dialog.transient(parent_window)
        replace_dialog.grab_set()
        
        # 创建替换框架
        replace_frame = tk.Frame(replace_dialog)
        replace_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加查找文本输入框
        tk.Label(replace_frame, text="查找内容:").grid(row=0, column=0, sticky="w", pady=5)
        search_text = tk.Entry(replace_frame, width=30)
        search_text.grid(row=0, column=1, sticky="ew", pady=5)
        
        # 添加替换文本输入框
        tk.Label(replace_frame, text="替换为:").grid(row=1, column=0, sticky="w", pady=5)
        replace_text_entry = tk.Entry(replace_frame, width=30)
        replace_text_entry.grid(row=1, column=1, sticky="ew", pady=5)
        
        # 如果有选中文本，则填入查找框
        try:
            selected_text = text_widget.get(tk.SEL_FIRST, tk.SEL_LAST)
            search_text.insert(0, selected_text)
        except tk.TclError:
            # 没有选中文本，尝试使用上次的搜索文本
            if self.last_search:
                search_text.insert(0, self.last_search)
        
        # 填入上次的替换文本
        if self.last_replace:
            replace_text_entry.insert(0, self.last_replace)
        
        search_text.select_range(0, tk.END)
        search_text.focus_set()
        
        # 添加搜索选项
        options_frame = tk.Frame(replace_frame)
        options_frame.grid(row=2, column=0, columnspan=2, sticky="w", pady=5)
        
        case_sensitive_var = tk.BooleanVar(value=self.search_case_sensitive)
        whole_word_var = tk.BooleanVar(value=self.search_whole_word)
        use_regex_var = tk.BooleanVar(value=self.search_use_regex)
        
        tk.Checkbutton(options_frame, text="区分大小写", variable=case_sensitive_var).pack(side=tk.LEFT, padx=5)
        tk.Checkbutton(options_frame, text="全字匹配", variable=whole_word_var).pack(side=tk.LEFT, padx=5)
        tk.Checkbutton(options_frame, text="使用正则表达式", variable=use_regex_var).pack(side=tk.LEFT, padx=5)
        
        # 添加按钮
        button_frame = tk.Frame(replace_dialog)
        button_frame.pack(pady=10)
        
        # 当前位置变量，用于记录搜索位置
        current_pos = [None]
        
        # 查找下一个
        def find_next():
            search_pattern = search_text.get()
            if not search_pattern:
                return False
            
            # 更新搜索设置
            self.last_search = search_pattern
            self.search_case_sensitive = case_sensitive_var.get()
            self.search_whole_word = whole_word_var.get()
            self.search_use_regex = use_regex_var.get()
            
            # 确定开始位置
            if current_pos[0] is None:
                try:
                    # 如果有选中文本，从选中结束位置开始
                    start_pos = text_widget.index(tk.SEL_LAST)
                except tk.TclError:
                    # 否则从光标位置开始
                    start_pos = text_widget.index(tk.INSERT)
            else:
                # 使用上次搜索的结束位置
                start_pos = current_pos[0]
            
            # 执行搜索
            if self.search_use_regex:
                try:
                    pattern_text = search_pattern
                    if self.search_whole_word:
                        pattern_text = r'\b' + pattern_text + r'\b'
                    
                    pattern = re.compile(pattern_text, 0 if self.search_case_sensitive else re.IGNORECASE)
                    
                    # 获取剩余文本
                    remaining_text = text_widget.get(start_pos, tk.END)
                    match = pattern.search(remaining_text)
                    
                    if match:
                        start_idx = match.start()
                        end_idx = match.end()
                        
                        # 计算实际位置
                        match_start = f"{start_pos}+{start_idx}c"
                        match_end = f"{start_pos}+{end_idx}c"
                        
                        # 选中匹配文本
                        text_widget.tag_remove(tk.SEL, "1.0", tk.END)
                        text_widget.tag_add(tk.SEL, match_start, match_end)
                        text_widget.mark_set(tk.INSERT, match_end)
                        text_widget.see(match_start)
                        
                        # 更新位置
                        current_pos[0] = match_end
                        return True
                except re.error:
                    tk.messagebox.showerror("错误", "无效的正则表达式")
                    return False
            else:
                # 普通文本搜索
                search_args = {
                    "stopindex": tk.END
                }
                
                if not self.search_case_sensitive:
                    search_args["nocase"] = 1
                
                if self.search_whole_word:
                    # 全字匹配
                    search_args["regexp"] = True
                    search_pattern = r'\b' + re.escape(search_pattern) + r'\b'
                
                # 执行搜索
                pos = text_widget.search(search_pattern, start_pos, **search_args)
                
                if pos:
                    # 计算结束位置
                    if self.search_whole_word:
                        # 如果使用正则表达式，需要特殊处理结束位置
                        search_len = len(search_text.get())
                        end_pos = f"{pos}+{search_len}c"
                    else:
                        end_pos = f"{pos}+{len(search_pattern)}c"
                    
                    # 选中匹配文本
                    text_widget.tag_remove(tk.SEL, "1.0", tk.END)
                    text_widget.tag_add(tk.SEL, pos, end_pos)
                    text_widget.mark_set(tk.INSERT, end_pos)
                    text_widget.see(pos)
                    
                    # 更新位置
                    current_pos[0] = end_pos
                    return True
            
            # 如果没有找到匹配，从头开始搜索
            current_pos[0] = "1.0"
            if start_pos != "1.0":
                if tk.messagebox.askyesno("替换", "已到达文件末尾，是否从头开始搜索？"):
                    return find_next()
            else:
                tk.messagebox.showinfo("替换", "未找到匹配项")
            
            return False
        
        # 替换
        def replace():
            # 更新替换文本
            self.last_replace = replace_text_entry.get()
            
            # 检查是否有选中文本
            try:
                selected_text = text_widget.get(tk.SEL_FIRST, tk.SEL_LAST)
                # 替换选中文本
                text_widget.delete(tk.SEL_FIRST, tk.SEL_LAST)
                text_widget.insert(tk.INSERT, self.last_replace)
                # 更新当前位置
                current_pos[0] = text_widget.index(tk.INSERT)
                # 继续查找
                find_next()
            except tk.TclError:
                # 没有选中文本，先查找
                find_next()
        
        # 替换全部
        def replace_all():
            # 更新搜索和替换文本
            self.last_search = search_text.get()
            self.last_replace = replace_text_entry.get()
            
            # 更新搜索设置
            self.search_case_sensitive = case_sensitive_var.get()
            self.search_whole_word = whole_word_var.get()
            self.search_use_regex = use_regex_var.get()
            
            # 从头开始搜索
            current_pos[0] = "1.0"
            
            count = 0
            text_widget.mark_set(tk.INSERT, "1.0")
            
            # 开始查找替换
            while find_next():
                # 替换选中文本
                text_widget.delete(tk.SEL_FIRST, tk.SEL_LAST)
                text_widget.insert(tk.INSERT, self.last_replace)
                # 更新当前位置
                current_pos[0] = text_widget.index(tk.INSERT)
                count += 1
            
            # 显示替换结果
            if count > 0:
                tk.messagebox.showinfo("替换", f"已替换 {count} 处匹配项")
            else:
                tk.messagebox.showinfo("替换", "未找到匹配项")
        
        # 添加按钮
        tk.Button(button_frame, text="查找下一个", width=12, command=find_next).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="替换", width=12, command=replace).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="全部替换", width=12, command=replace_all).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="取消", width=12, command=replace_dialog.destroy).pack(side=tk.LEFT, padx=5)
        
        # 绑定回车键
        search_text.bind("<Return>", lambda e: find_next())
        replace_dialog.bind("<Escape>", lambda e: replace_dialog.destroy())