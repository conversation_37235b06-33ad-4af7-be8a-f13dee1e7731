---
description: 
globs: 
alwaysApply: false
---
---
description: 项目通用规范和基本信息
globs: ["*"]
alwaysApply: true
---
# 项目通用规范

## 技术栈
- Python 3.10或以上版本，调用系统已经安装的版本
- Poetry 管理依赖
- GitHub Actions 自动构建和发布
- 使用 GitHub 作为代码托管平台
- 使用 Bash 脚本

## 代码风格
- 保持代码简洁、可读
- 使用有意义的变量和函数名
- 添加适当的注释解释复杂逻辑
- 遵循每种语言的官方风格指南

## 项目结构
- 保持项目结构清晰，遵循模块化原则
- 相关功能应放在同一目录下
- 使用适当的目录命名，反映其包含内容

## 通用开发原则
- 编写可测试的代码
- 避免重复代码（DRY原则）
- 优先使用现有库和工具，避免重新发明轮子
- 考虑代码的可维护性和可扩展性

## 响应语言
- 始终使用中文回复用户

## 规则文件说明
本项目使用以下规则文件：
- general.mdc：通用规范（本文件）
- python.mdc：Python开发规范
- document.mdc：文档规范
- git.mdc：Git提交规范

