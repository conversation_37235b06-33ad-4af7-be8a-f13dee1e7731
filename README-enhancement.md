# 记事本编码功能增强

## 新增功能

本次更新为记事本应用添加了两个重要的编码相关功能：

1. **编码转换功能**：允许用户将当前打开的文件从一种编码转换为另一种编码
2. **保存时选择编码**：在另存为文件时，提供编码选择对话框

## 使用方法

### 编码转换

1. 打开需要转换编码的文件
2. 在**文件**菜单中选择**转换编码(E)...**选项
3. 在弹出的对话框中选择目标编码
4. 点击**转换**按钮完成转换

编码转换后，文件会被标记为已修改状态，需要手动保存才能将转换后的内容写入文件。

### 保存时选择编码

1. 编辑文件内容
2. 在**文件**菜单中选择**另存为(A)...**选项
3. 选择保存路径和文件名
4. 在弹出的编码选择对话框中选择目标编码
5. 点击**确定**按钮完成保存

## 支持的编码

应用支持以下常用编码：

- UTF-8（Unicode，无BOM）
- UTF-8-SIG（Unicode，带BOM）
- GBK（简体中文）
- GB2312（简体中文）
- GB18030（中文国家标准）
- BIG5（繁体中文）
- Shift-JIS（日文）
- EUC-JP（日文）
- EUC-KR（韩文）
- ISO-8859-1（西欧语言）
- Latin-1（西欧语言）
- ASCII（美国标准）

## 部署方法

有两种方式可以添加这些编码功能：

### 方法一：使用脚本自动添加

1. 将`add_encoding_functions.py`脚本复制到项目根目录
2. 运行该脚本：`python add_encoding_functions.py`
3. 脚本会自动向`main.py`添加所需的代码

### 方法二：手动添加代码

1. 在`main.py`的导入部分添加：
   ```python
   from src.utils.encoding import EncodingDetector
   ```

2. 在文件菜单中添加"转换编码"选项（在"另存为"和"退出"之间）

3. 将`add_encoding_functions.patch`文件中的`_save_as_file`和`_convert_encoding`方法复制到`main.py`中

## 注意事项

- 编码转换可能导致一些特殊字符无法正确显示，这是由于不同编码之间的兼容性问题导致的
- 某些编码（如ASCII）支持的字符集有限，转换为这些编码时可能会丢失部分字符
- 转换编码前建议先保存文件，以防数据丢失 