# 记事本软件 PRD 文档

## 一、引言
1.1 项目背景  
系统自带的记事本在功能和用户体验上存在一定局限，为满足用户对文本编辑更高的要求，开发一款新的记事本软件，旨在提供更便捷、高效的文本编辑功能。

1.2 文档目的  
明确记事本软件的各项功能需求，为开发团队提供详细的开发依据，确保软件能达到预期的功能和用户体验标准。

## 二、功能概述
本软件是一款基于 Python 语言开发，运行于 Windows 系统的记事本软件。它具备常见的文本编辑功能，如文件的新建、打开、保存等，同时注重用户体验，在退出时会提醒保存未保存的文件。界面采用中文显示，编码为 UTF - 8，且 UI 界面风格与 Windows 系统自带的记事本相似。

## 三、功能需求
### 3.1 文件操作
#### 3.1.1 新建文件
用户可通过菜单栏中的 "文件" - "新建" 选项，或使用快捷键 Ctrl + N 来创建一个新的空白文本文件。
新文件默认采用 UTF - 8 编码。

#### 3.1.2 打开文件
用户能通过 "文件" - "打开" 选项，或使用快捷键 Ctrl + O 打开文件选择对话框，从本地磁盘选择要打开的文本文件。
软件支持常见的文本文件格式，如 .txt、.log 等。
打开文件时，系统会自动检测文件编码，优先尝试以 UTF - 8 编码打开；若检测失败，会弹出提示框让用户手动选择合适的编码。

#### 3.1.3 保存文件
用户可通过 "文件" - "保存" 选项，或使用快捷键 Ctrl + S 保存当前正在编辑的文件。
若文件是新建且尚未保存过，软件会弹出 "另存为" 对话框，让用户指定保存路径和文件名。
文件保存时采用 UTF - 8 编码。

#### 3.1.4 另存为
用户可以通过 "文件" - "另存为" 选项，将当前编辑的文件以新的路径和文件名进行保存。
保存时同样采用 UTF - 8 编码。

#### 3.1.5 退出软件
当用户点击软件窗口的关闭按钮，或选择 "文件" - "退出" 选项时，软件会检查所有打开的文件是否有未保存的更改。
若存在未保存的文件，会弹出提示框，询问用户是否保存更改，提供 "保存"、"不保存" 和 "取消" 三个选项：
选择 "保存"：软件会保存所有未保存的文件，然后退出。
选择 "不保存"：直接退出软件，不保存未保存的更改。
选择 "取消"：取消退出操作，用户可继续编辑文件。

### 3.2 文本编辑
#### 3.2.1 文本输入与删除
用户可以在编辑区域自由输入文本，支持全键盘字符输入，包括字母、数字、标点符号等。
可以使用 Backspace 键删除光标前的字符，使用 Delete 键删除光标后的字符。

#### 3.2.2 复制、粘贴和剪切
用户可通过 "编辑" - "复制" 选项（快捷键 Ctrl + C）复制选中的文本。
通过 "编辑" - "粘贴" 选项（快捷键 Ctrl + V）将复制或剪切的文本粘贴到指定位置。
通过 "编辑" - "剪切" 选项（快捷键 Ctrl + X）将选中的文本剪切到剪贴板。

#### 3.2.3 撤销和恢复
用户可以使用 "编辑" - "撤销" 选项（快捷键 Ctrl + Z）撤销上一步操作。
使用 "编辑" - "恢复" 选项（快捷键 Ctrl + Y）恢复已撤销的操作。

#### 3.2.4 查找和替换
用户通过 "编辑" - "查找" 选项（快捷键 Ctrl + F）打开查找对话框，输入要查找的文本，软件会在当前编辑的文件中查找匹配项，并高亮显示。
通过 "编辑" - "替换" 选项（快捷键 Ctrl + H）打开替换对话框，用户可以输入要查找的文本和替换的文本，进行查找和替换操作。

### 3.3 界面显示
#### 3.3.1 界面语言
软件界面所有菜单、按钮、提示信息等均使用中文显示。

#### 3.3.2 文件展示窗口
界面顶部为文件展示窗口，采用标签页形式展示已保存的和打开的文件。
每个标签页显示文件的名称，用户点击标签页可切换不同的文件进行编辑。
未保存的文件不会在该窗口显示。

#### 3.3.3 编辑区域
软件主体部分为编辑区域，用于显示和编辑文本内容。
编辑区域具有垂直和水平滚动条，方便用户查看长文本。

#### 3.3.4 UI 风格
UI 界面整体风格与 Windows 系统自带的记事本相似，包括窗口布局、菜单样式、按钮外观等，以降低用户的学习成本，提高操作的便捷性。

### 3.4 打包与安装
1. **打包为独立可执行文件**  
    - 支持使用 PyInstaller 将 Python 代码打包为单个 `.exe` 文件，确保无需安装 Python 环境即可运行。  
    - 打包时需包含所有依赖库（如 Tkinter、chardet、pyperclip）及资源文件（如图标、配置文件）。  

2. **生成安装程序**  
    - **推荐使用 `.exe` 格式安装程序**，支持用户自定义安装路径、创建桌面快捷方式和开始菜单条目。  
    - 安装程序需包含软件许可证协议和卸载功能。

## 四、非功能需求
4.1 性能需求  
软件应具备良好的响应性能，在进行文件打开、保存、编辑等操作时，响应时间应控制在合理范围内，避免出现明显的卡顿。
对于较大的文本文件，软件应能快速加载和处理，确保流畅的编辑体验。

4.2 可维护性需求  
软件的代码结构应清晰，具有良好的可读性和可维护性，方便后续的功能扩展和 bug 修复。

### 4.3 打包与兼容性
1. **兼容性**  
    - 生成的 `.exe` 文件需兼容 Windows 10 及以上系统（64 位）。  
    - 安装程序需支持静默安装（如 `/S` 参数），并在 Windows 10 以下系统或 32 位系统中禁止安装。  

2. **文件完整性**  
    - 打包后的文件大小需合理，避免冗余依赖。  
    - 验证 `.exe` 文件在解压后无缺失文件或损坏。

## 五、技术方案
5.1 开发环境  
| 名称 | 版本 | 说明 |
| ---- | ---- | ---- |
| Python | 3.8+ | 开发语言 |
| Tkinter | 内置 | GUI 框架 |
| chardet | 4.0.0+ | 编码检测库 |
| pyperclip | 1.8.2+ | 剪贴板操作 |

5.2 依赖工具  
| 工具         | 说明                     |
|--------------|--------------------------|
| PyCharm/VS Code | 开发工具                 |
| Git          | 版本控制                 |
| **PyInstaller** | 打包工具（需在 `requirements.txt` 中添加） |
| pytest       | 测试框架                 |

5.3 文件架构  
```plaintext
notepad_software/
├── src/
│   ├── main.py          # 主程序入口
│   ├── editor.py        # 文本编辑逻辑
│   └── file_handler.py  # 文件操作模块
├── resources/
│   └── icons/           # 应用图标
├── tests/
│   ├── test_editor.py   # 编辑功能测试
│   └── test_file.py     # 文件操作测试
├── docs/
│   ├── PRD.md           # 当前文档
│   └── requirements.txt # 依赖清单
└── config/
    └── settings.json    # 配置文件

5.4 打包配置
**PyInstaller 配置**
使用 pyinstaller --onefile --windowed --icon=resources/icons/notepad.ico main.py 命令打包。
通过 --add-data 参数包含资源文件（如配置文件、图标）。
**安装程序工具**
推荐使用 Inno Setup 或 NSIS 生成安装包，配置安装界面、快捷方式和卸载逻辑。
在安装程序中添加系统版本检查，确保仅支持 Windows 10 及以上 64 位系统。

##六、原型设计流程
**6.1 需求理解与分析**
仔细研读本 PRD 文档，明确记事本软件的各项功能需求、非功能需求以及目标用户群体。与产品经理、开发团队进行沟通，澄清任何模糊或不确定的需求点。例如，针对文件操作中的编码检测与手动选择功能，确保理解其具体实现方式和用户交互逻辑。
**6.2 信息架构设计**
根据功能需求，设计软件的信息架构。确定各个功能模块之间的层级关系和导航逻辑。对于记事本软件，可将主要功能模块分为文件操作、文本编辑和界面显示。在文件操作模块下，细分新建、打开、保存等子功能。绘制信息架构图，直观展示软件的结构。
**6.3 草图绘制**
使用纸笔或简单的绘图工具，快速绘制软件界面的草图。从整体布局开始，确定窗口的大小、各个区域的位置和大致比例。例如，设计文件展示窗口、编辑区域和菜单栏的位置。重点关注用户操作流程和信息展示方式，确保界面简洁易用。草图可以是粗略的线条和简单的文字标注，主要用于快速验证设计思路。
**6.4 原型工具选择与创建**
根据项目需求和团队技术栈，选择合适的原型设计工具，使用Figma。使用所选工具将功能需求转化为可交互的原型。按照信息架构设计，创建各个页面和界面元素。设置元素之间的交互效果，如点击、滑动、跳转等。例如，实现点击 "新建文件" 按钮后，弹出空白编辑页面的交互。
**6.5 交互设计与优化**
根据功能需求，设计详细的交互逻辑。例如，在查找和替换功能中，设置输入查找内容后，自动高亮显示匹配项的交互效果。对原型进行用户测试，邀请目标用户或相关人员进行试用，收集反馈意见。根据反馈对原型进行优化，调整界面布局、交互方式等，提高用户体验。

##七、验收标准
**6.1 功能验收**
所有功能需求中的功能均能正常实现，无明显的功能缺陷。
退出软件时，能正确提示用户保存未保存的文件。
界面语言为中文，文件展示窗口仅显示已保存的文件。
UI 界面风格与 Windows 系统自带的记事本相似。
**6.2 性能验收**
软件在进行各种操作时，响应时间符合性能需求的要求。
**6.3 打包验收**
打包后的 .exe 文件可在 Windows 10 及以上 64 位系统直接运行，所有功能正常。
安装程序支持完整的安装、卸载流程，且无残留文件；静默安装成功率需达到 100%。
软件图标、名称、版本信息与配置文件一致。
安装程序在 Windows 10 以下系统或 32 位系统中无法启动，并提示兼容性错误。

##八、关键修正说明
依赖清单补充
在 requirements.txt 中添加 PyInstaller。
验收标准强化
新增系统版本拦截测试（第 6.3.4 条）。
新增静默安装成功率验证（第 6.3.2 条）。
术语统一
明确安装程序默认格式为 .exe（第 3.4.2 条）。
表述优化
修正重复的 "一致" 表述（第 6.3.3 条）。
