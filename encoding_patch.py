"""
本文件包含记事本应用添加编码转换功能所需的所有代码修改。
请按照以下步骤将代码片段添加到相应文件中。
"""

#=====================================================
# 第1步：在src/main.py中添加导入语句
#=====================================================
"""
确保在main.py的导入部分包含以下导入:

from src.utils.encoding import EncodingDetector
"""

#=====================================================
# 第2步：添加转换编码菜单项
#=====================================================
"""
在main.py的_create_menu方法中，找到文件菜单项的定义部分，
在"另存为"和"退出"选项之间添加转换编码选项:

self.file_menu.add_command(label="另存为(A)...", underline=4, command=self._save_as_file)
self.file_menu.add_separator()
self.file_menu.add_command(label="转换编码(E)...", underline=4, command=self._convert_encoding)
self.file_menu.add_separator()
self.file_menu.add_command(label="退出(X)", underline=3, command=self._on_closing)
"""

#=====================================================
# 第3步：添加_convert_encoding方法
#=====================================================
"""
在main.py文件末尾添加以下方法:
"""

def _convert_encoding(self):
    """转换当前文件的编码"""
    if self.active_tab_index >= len(self.open_files):
        return
        
    file_info = self.open_files[self.active_tab_index]
    text_area = file_info.get("text_widget")
    current_encoding = file_info.get("encoding", "UTF-8")
    
    # 创建编码转换对话框
    encoding_dialog = tk.Toplevel(self.root)
    encoding_dialog.title("转换文件编码")
    encoding_dialog.geometry("400x300")
    encoding_dialog.resizable(False, False)
    encoding_dialog.transient(self.root)
    encoding_dialog.grab_set()
    
    # 添加当前编码信息
    tk.Label(encoding_dialog, text=f"当前文件编码: {current_encoding}").pack(pady=10)
    tk.Label(encoding_dialog, text="选择目标编码:").pack(pady=5)
    
    # 从EncodingDetector获取可用编码列表
    from src.utils.encoding import EncodingDetector
    encodings = EncodingDetector.get_available_encodings()
    
    # 创建编码选择列表框
    encoding_listbox = tk.Listbox(encoding_dialog, height=10)
    for enc in encodings:
        encoding_listbox.insert(tk.END, enc)
    # 选中当前编码或默认UTF-8
    try:
        current_index = encodings.index(current_encoding.lower())
        encoding_listbox.select_set(current_index)
    except ValueError:
        encoding_listbox.select_set(0)  # 默认选中第一项
    encoding_listbox.pack(fill=tk.X, padx=20, pady=5)
    
    # 对话框结果
    dialog_result = {"action": None, "encoding": None}
    
    # 确定按钮回调
    def confirm_conversion():
        selected_index = encoding_listbox.curselection()
        if selected_index:
            dialog_result["action"] = "confirm"
            dialog_result["encoding"] = encodings[selected_index[0]]
        encoding_dialog.destroy()
    
    # 取消按钮回调
    def cancel():
        dialog_result["action"] = "cancel"
        encoding_dialog.destroy()
    
    # 创建按钮
    button_frame = tk.Frame(encoding_dialog)
    button_frame.pack(pady=10)
    tk.Button(button_frame, text="转换", width=10, command=confirm_conversion).pack(side=tk.LEFT, padx=10)
    tk.Button(button_frame, text="取消", width=10, command=cancel).pack(side=tk.LEFT, padx=10)
    
    # 等待对话框关闭
    self.root.wait_window(encoding_dialog)
    
    # 处理对话框结果
    if dialog_result["action"] == "confirm" and dialog_result["encoding"]:
        target_encoding = dialog_result["encoding"]
        
        # 如果目标编码与当前编码相同，不做处理
        if target_encoding.lower() == current_encoding.lower():
            messagebox.showinfo("提示", "目标编码与当前编码相同，无需转换。")
            return
        
        try:
            # 获取文本内容
            content = text_area.get(1.0, tk.END)
            
            # 使用EncodingDetector转换编码
            converted_content = EncodingDetector.convert_encoding(
                content, 
                from_encoding=current_encoding, 
                to_encoding=target_encoding
            )
            
            # 更新文本区域内容
            text_area.delete(1.0, tk.END)
            text_area.insert(1.0, converted_content)
            
            # 更新文件信息
            file_info["encoding"] = target_encoding
            file_info["modified"] = True
            
            # 更新窗口标题
            self._update_window_title()
            
            # 更新状态栏
            self.status_bar.config(text=f"编码已转换: {current_encoding} -> {target_encoding}")
            
            # 标记为已修改
            text_area.edit_modified(True)
            
            messagebox.showinfo("成功", f"文件编码已从 {current_encoding} 转换为 {target_encoding}")
        except Exception as e:
            messagebox.showerror("错误", f"编码转换失败: {str(e)}")

#=====================================================
# 第4步：替换_save_as_file方法
#=====================================================
"""
在main.py中找到_save_as_file方法，将其替换为以下带有编码选择的版本:
"""

def _save_as_file(self, event=None):
    """文件另存为，提供编码选择功能"""
    # 获取当前标签页信息
    if self.active_tab_index >= len(self.open_files):
        return False
            
    file_info = self.open_files[self.active_tab_index]
    text_area = file_info.get("text_widget")
    current_encoding = file_info.get("encoding", "UTF-8")
    
    # 打开保存文件对话框
    file_path = filedialog.asksaveasfilename(
        title="保存文件",
        defaultextension=".txt",
        filetypes=[
            ("文本文件", "*.txt"),
            ("日志文件", "*.log"),
            ("所有文件", "*.*")
        ]
    )
    
    if not file_path:  # 用户取消操作
        return False
    
    # 创建编码选择对话框
    encoding_dialog = tk.Toplevel(self.root)
    encoding_dialog.title("选择保存编码")
    encoding_dialog.geometry("400x250")
    encoding_dialog.resizable(False, False)
    encoding_dialog.transient(self.root)
    encoding_dialog.grab_set()
    
    # 添加提示信息
    tk.Label(encoding_dialog, text=f"选择文件保存编码:").pack(pady=10)
    
    # 从EncodingDetector获取可用编码列表
    from src.utils.encoding import EncodingDetector
    encodings = EncodingDetector.get_available_encodings()
    
    # 创建编码选择列表框
    encoding_listbox = tk.Listbox(encoding_dialog, height=7)
    for enc in encodings:
        encoding_listbox.insert(tk.END, enc)
    
    # 选中当前编码或默认UTF-8
    try:
        current_index = encodings.index(current_encoding.lower())
        encoding_listbox.select_set(current_index)
    except ValueError:
        encoding_listbox.select_set(0)  # 默认选中第一项
    
    encoding_listbox.pack(fill=tk.X, padx=20, pady=5)
    
    # 对话框结果
    dialog_result = {"action": None, "encoding": None}
    
    # 确定按钮回调
    def confirm_encoding():
        selected_index = encoding_listbox.curselection()
        if selected_index:
            dialog_result["action"] = "confirm"
            dialog_result["encoding"] = encodings[selected_index[0]]
        encoding_dialog.destroy()
    
    # 取消按钮回调
    def cancel():
        dialog_result["action"] = "cancel"
        encoding_dialog.destroy()
    
    # 创建按钮
    button_frame = tk.Frame(encoding_dialog)
    button_frame.pack(pady=10)
    tk.Button(button_frame, text="确定", width=10, command=confirm_encoding).pack(side=tk.LEFT, padx=10)
    tk.Button(button_frame, text="取消", width=10, command=cancel).pack(side=tk.LEFT, padx=10)
    
    # 等待对话框关闭
    self.root.wait_window(encoding_dialog)
    
    # 处理对话框结果
    if dialog_result["action"] == "confirm" and dialog_result["encoding"]:
        try:
            # 获取文本内容
            content = text_area.get(1.0, tk.END)
            target_encoding = dialog_result["encoding"]
            
            # 使用文件处理器保存文件，指定编码
            self.file_handler.save_file(file_path, content, encoding=target_encoding)
            
            # 更新文件信息
            file_info["path"] = file_path
            file_info["modified"] = False
            file_info["encoding"] = target_encoding
            
            # 更新标签页标题
            self.notebook.tab(self.active_tab_index, text=os.path.basename(file_path))
            
            # 更新窗口标题
            self._update_window_title()
            
            # 更新状态栏
            self.status_bar.config(text=f"已保存: {file_path} (编码: {target_encoding})")
            
            # 重置修改标记
            text_area.edit_modified(False)
            
            return True
            
        except Exception as e:
            messagebox.showerror("错误", f"无法保存文件: {str(e)}")
    
    return False

#=====================================================
# 第5步：也为_save_file方法添加编码选择（可选）
#=====================================================
"""
如果希望在普通保存文件时也提供编码选择功能，可以修改_save_file方法。
这里不提供具体实现，因为通常保存已存在的文件时会使用原有编码。
但您可以参考_save_as_file方法的实现来添加此功能。
""" 