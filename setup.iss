#define MyAppName "中文记事本"
#define MyAppVersion "1.0"
#define MyAppPublisher "Python爱好者"
#define MyAppExeName "Notepad.exe"

[Setup]
; 注意: AppId的值为唯一标识符
AppId={{8F52090C-7F1C-4A78-9D27-77AA2B4C8DD1}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
DefaultDirName={autopf}\{#MyAppName}
DisableProgramGroupPage=yes
; 取消下面的注释以允许用户选择安装目录
DisableDirPage=no
OutputDir=installer
OutputBaseFilename=notepad_setup
Compression=lzma
SolidCompression=yes
; 需要管理员权限
PrivilegesRequired=admin

[Languages]
; 首先使用默认的英文
Name: "english"; MessagesFile: "compiler:Default.isl"
; 如果有中文语言文件则使用中文
Name: "chinesesimp"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Messages]
; 为英文界面提供中文翻译
english.BeveledLabel=Chinese Notepad
english.ButtonNext=下一步(&N) >
english.ButtonBack=< 上一步(&B)
english.ButtonCancel=取消(&C)
english.ButtonInstall=安装(&I)
english.ButtonFinish=完成(&F)
english.SelectDirLabel3=安装程序将安装 [name] 到下列文件夹。
english.SelectDirBrowseLabel=点击"下一步"继续。如果要选择其他文件夹，请点击"浏览"。

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "dist\Notepad\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\Notepad\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent