import os
import chardet

class FileHandler:
    """文件操作处理类"""
    
    def __init__(self):
        """初始化文件处理器"""
        # 默认编码
        self.default_encoding = "utf-8"
    
    def open_file(self, file_path):
        """打开文件并读取内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            tuple: (文件内容, 文件编码)
            
        Raises:
            FileNotFoundError: 文件不存在
            IOError: 读取文件失败
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            # 以二进制模式读取文件
            with open(file_path, 'rb') as file:
                raw_data = file.read()
            
            # 检测文件编码
            result = chardet.detect(raw_data)
            encoding = result['encoding'] if result['confidence'] > 0.7 else self.default_encoding
            
            # 使用检测到的编码解码文件内容
            content = raw_data.decode(encoding)
            
            return content, encoding
            
        except Exception as e:
            raise IOError(f"读取文件失败: {str(e)}")
    
    def save_file(self, file_path, content, encoding=None):
        """保存内容到文件
        
        Args:
            file_path: 文件路径
            content: 文件内容
            encoding: 文件编码，默认为UTF-8
            
        Raises:
            IOError: 保存文件失败
        """
        if encoding is None:
            encoding = self.default_encoding
        
        try:
            # 以指定编码保存文件
            with open(file_path, 'w', encoding=encoding) as file:
                file.write(content)
                
        except Exception as e:
            raise IOError(f"保存文件失败: {str(e)}")