import os
import sys
import tkinter as tk
from tkinter import messagebox, filedialog, font
import tkinter.ttk as ttk
import json

# 导入自定义模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.editor import Editor
from src.file_handler import FileHandler

class NotepadApp:
    """记事本应用主窗口类"""
    
    def __init__(self, root):
        """初始化记事本应用
        
        Args:
            root: Tkinter主窗口对象
        """
        self.root = root
        self.editor = Editor()
        self.file_handler = FileHandler()
        
        # 当前打开的文件路径列表
        self.open_files = []
        
        # 当前活动的标签页索引
        self.active_tab_index = 0
        
        # 加载配置
        self.config = self._load_config()
        
        # 设置窗口基本属性
        self._setup_window()
        
        # 创建菜单栏
        self._create_menu()
        
        # 创建主界面组件
        self._create_widgets()
        
        # 绑定事件
        self._bind_events()
        
        # 应用配置
        self._apply_settings()
        
        # 创建新标签页
        self._new_file()
    
    def _setup_window(self):
        """设置窗口基本属性"""
        # 设置窗口标题
        self.root.title("记事本")
        
        # 设置窗口图标（如果存在）
        icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                "resources", "icons", "notepad.ico")
        if os.path.exists(icon_path):
            self.root.iconbitmap(icon_path)
        
        # 设置窗口默认尺寸和位置
        window_width = 800
        window_height = 600
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 计算窗口居中的坐标
        x_position = (screen_width - window_width) // 2
        y_position = (screen_height - window_height) // 2
        
        # 设置窗口大小和位置
        self.root.geometry(f"{window_width}x{window_height}+{x_position}+{y_position}")
        
        # 设置窗口最小尺寸
        self.root.minsize(400, 300)
        
        # 配置窗口样式，使其符合Windows记事本风格
        self.root.configure(bg="white")
        
        # 设置窗口关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_menu(self):
        """创建菜单栏"""
        # 创建主菜单栏
        self.menu_bar = tk.Menu(self.root)
        self.root.config(menu=self.menu_bar)
        
        # 创建文件菜单
        self.file_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="文件(F)", underline=3, menu=self.file_menu)
        self.file_menu.add_command(label="新建(N)", underline=3, command=self._new_file, accelerator="Ctrl+N")
        self.file_menu.add_command(label="打开(O)...", underline=3, command=self._open_file, accelerator="Ctrl+O")
        self.file_menu.add_command(label="保存(S)", underline=3, command=self._save_file, accelerator="Ctrl+S")
        self.file_menu.add_command(label="另存为(A)...", underline=4, command=self._save_as_file)
        self.file_menu.add_separator()
        self.file_menu.add_command(label="退出(X)", underline=3, command=self._on_closing)
        
        # 创建编辑菜单
        self.edit_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="编辑(E)", underline=3, menu=self.edit_menu)
        self.edit_menu.add_command(label="撤销(U)", underline=3, command=self._undo, accelerator="Ctrl+Z")
        self.edit_menu.add_command(label="恢复(R)", underline=3, command=self._redo, accelerator="Ctrl+Y")
        self.edit_menu.add_separator()
        self.edit_menu.add_command(label="剪切(T)", underline=3, command=self._cut, accelerator="Ctrl+X")
        self.edit_menu.add_command(label="复制(C)", underline=3, command=self._copy, accelerator="Ctrl+C")
        self.edit_menu.add_command(label="粘贴(P)", underline=3, command=self._paste, accelerator="Ctrl+V")
        self.edit_menu.add_command(label="删除(L)", underline=3, command=self._delete, accelerator="Del")
        self.edit_menu.add_separator()
        self.edit_menu.add_command(label="查找(F)...", underline=3, command=self._find, accelerator="Ctrl+F")
        self.edit_menu.add_command(label="替换(R)...", underline=3, command=self._replace, accelerator="Ctrl+H")
        self.edit_menu.add_separator()
        self.edit_menu.add_command(label="全选(A)", underline=3, command=self._select_all, accelerator="Ctrl+A")
        self.edit_menu.add_command(label="时间/日期(D)", underline=6, command=self._insert_datetime, accelerator="F5")
        
        # 创建格式菜单
        self.format_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="格式(O)", underline=3, menu=self.format_menu)
        
        # 自动换行选项（使用Checkbutton）
        self.word_wrap_var = tk.BooleanVar()
        self.word_wrap_var.set(self.config.get("word_wrap", True))  # 从配置中读取自动换行设置
        self.format_menu.add_checkbutton(label="自动换行(W)", underline=5, 
                                         variable=self.word_wrap_var, 
                                         command=self._toggle_word_wrap)
        
        self.format_menu.add_command(label="字体(F)...", underline=3, command=self._choose_font)
        
        # 创建查看菜单
        self.view_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="查看(V)", underline=3, menu=self.view_menu)
        
        # 状态栏选项（使用Checkbutton）
        self.status_bar_var = tk.BooleanVar()
        self.status_bar_var.set(self.config.get("show_status_bar", True))  # 从配置中读取状态栏显示设置
        self.view_menu.add_checkbutton(label="状态栏(S)", underline=4, 
                                       variable=self.status_bar_var, 
                                       command=self._toggle_status_bar)
        
        # 创建帮助菜单
        self.help_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="帮助(H)", underline=3, menu=self.help_menu)
        self.help_menu.add_command(label="查看帮助(H)", underline=5, command=self._show_help)
        self.help_menu.add_separator()
        self.help_menu.add_command(label="关于记事本(A)", underline=5, command=self._show_about)
    
    def _create_widgets(self):
        """创建主界面组件"""
        # 创建主框架
        self.main_frame = tk.Frame(self.root, bg="white")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标签页控件
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 绑定标签页切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self._on_tab_changed)
        
        # 绑定标签页右键菜单
        self.notebook.bind("<Button-3>", self._show_tab_popup_menu)
        
        # 创建状态栏
        self.status_bar = tk.Label(self.root, text="就绪", bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def _create_text_widget(self, tab_frame):
        """为标签页创建文本编辑区域
        
        Args:
            tab_frame: 标签页框架
            
        Returns:
            文本编辑区域控件
        """
        # 创建文本编辑区域
        text_area = tk.Text(
            tab_frame,
            wrap=tk.WORD if self.word_wrap_var.get() else tk.NONE,
            undo=True,  # 启用撤销功能
            maxundo=1000,  # 设置最大撤销次数
            autoseparators=True,  # 自动添加撤销分隔符
            font=("宋体", 12),
            bg="white",
            fg="black",
            insertbackground="black",  # 光标颜色
            selectbackground="#ADD8E6",  # 选中文本的背景色
            selectforeground="black",  # 选中文本的前景色
            padx=5,  # 文本左右内边距
            pady=5   # 文本上下内边距
        )
        
        # 创建滚动条
        scrollbar_y = tk.Scrollbar(tab_frame, orient=tk.VERTICAL, command=text_area.yview)
        scrollbar_x = tk.Scrollbar(tab_frame, orient=tk.HORIZONTAL, command=text_area.xview)
        text_area.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 放置组件
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        text_area.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定事件
        text_area.bind("<<Modified>>", lambda e: self._on_text_modified(e, text_area))
        text_area.bind("<Button-3>", lambda e: self._show_popup_menu(e, text_area))
        text_area.bind("<ButtonRelease-1>", lambda e: self._update_status_bar(e, text_area))
        text_area.bind("<KeyRelease>", lambda e: self._update_status_bar(e, text_area))
        
        # 绑定按键事件 - 文件操作
        text_area.bind("<Control-n>", lambda e: self._new_file())
        text_area.bind("<Control-o>", lambda e: self._open_file())
        text_area.bind("<Control-s>", lambda e: self._save_file())
        text_area.bind("<Control-Shift-s>", lambda e: self._save_as_file())
        
        # 绑定按键事件 - 编辑操作
        text_area.bind("<Control-z>", lambda e: self._undo(text_area))
        text_area.bind("<Control-y>", lambda e: self._redo(text_area))

        # 绑定复制粘贴快捷键，并阻止默认行为
        def handle_cut(event):
            self._cut(text_area)
            return "break"
            
        def handle_copy(event):
            self._copy(text_area)
            return "break"
            
        def handle_paste(event):
            self._paste(text_area)
            return "break"

        text_area.bind("<Control-x>", handle_cut)
        text_area.bind("<Control-c>", handle_copy)
        text_area.bind("<Control-v>", handle_paste)

        text_area.bind("<Delete>", lambda e: self._delete(text_area))
        text_area.bind("<Control-f>", lambda e: self._find(text_area))
        text_area.bind("<Control-h>", lambda e: self._replace(text_area))
        text_area.bind("<Control-a>", lambda e: self._select_all(text_area))
        text_area.bind("<F5>", lambda e: self._insert_datetime(text_area))
        
        # 设置Tab键宽度（默认为8个字符）
        text_area.config(tabs=("0.5c", "1c", "1.5c", "2c"))
        
        return text_area
    
    def _on_tab_changed(self, event=None):
        """标签页切换事件处理"""
        # 获取当前选中的标签页索引
        selected_tab = self.notebook.select()
        if selected_tab:
            self.active_tab_index = self.notebook.index(selected_tab)
            
            # 更新窗口标题
            self._update_window_title()
            
            # 更新状态栏
            self._update_status_bar()
    
    def _update_window_title(self):
        """更新窗口标题"""
        if self.active_tab_index < len(self.open_files):
            file_info = self.open_files[self.active_tab_index]
            file_path = file_info.get("path")
            is_modified = file_info.get("modified", False)
            
            if file_path:
                title = f"{os.path.basename(file_path)} - 记事本"
                if is_modified:
                    title = f"*{title}"
            else:
                title = "无标题 - 记事本"
                if is_modified:
                    title = f"*{title}"
            
            self.root.title(title)
        else:
            self.root.title("记事本")
    
    def _show_tab_popup_menu(self, event):
        """显示标签页右键菜单"""
        # 获取鼠标点击的标签页索引
        try:
            clicked_tab = self.notebook.index(f"@{event.x},{event.y}")
            
            # 创建右键菜单
            tab_menu = tk.Menu(self.root, tearoff=0)
            tab_menu.add_command(label="关闭", command=lambda: self._close_tab(clicked_tab))
            tab_menu.add_command(label="关闭其他标签页", command=lambda: self._close_other_tabs(clicked_tab))
            tab_menu.add_separator()
            tab_menu.add_command(label="在资源管理器中打开", command=lambda: self._open_in_explorer(clicked_tab))
            
            # 显示菜单
            try:
                tab_menu.tk_popup(event.x_root, event.y_root, 0)
            finally:
                tab_menu.grab_release()
        except:
            pass
    
    def _new_file(self, event=None):
        """创建新文件"""
        # 创建新标签页框架
        tab_frame = tk.Frame(self.notebook)
        
        # 创建文本编辑区域
        text_area = self._create_text_widget(tab_frame)
        
        # 添加标签页
        self.notebook.add(tab_frame, text="无标题")
        
        # 切换到新标签页
        self.notebook.select(self.notebook.tabs()[-1])
        
        # 更新文件信息
        self.open_files.append({
            "path": None,
            "modified": False,
            "text_widget": text_area
        })
        
        # 更新活动标签页索引
        self.active_tab_index = len(self.open_files) - 1
        
        # 更新窗口标题
        self._update_window_title()
        
        # 更新状态栏
        self._update_status_bar()
        
        return True
    
    def _open_file(self, event=None):
        """打开文件"""
        # 打开文件对话框
        file_path = filedialog.askopenfilename(
            title="打开文件",
            filetypes=[
                ("文本文件", "*.txt"),
                ("日志文件", "*.log"),
                ("所有文件", "*.*")
            ]
        )
        
        if not file_path:  # 用户取消操作
            return False
        
        # 检查文件是否已经打开
        for i, file_info in enumerate(self.open_files):
            if file_info.get("path") == file_path:
                # 切换到已打开的标签页
                self.notebook.select(i)
                return True
        
        try:
            # 使用文件处理器打开文件
            content, encoding = self.file_handler.open_file(file_path)
            # 成功打开文件，继续处理
            return self._process_opened_file(file_path, content, encoding)
        except Exception as e:
            # 文件打开失败，可能是编码问题
            return self._handle_encoding_error(file_path, str(e))
    
    def _process_opened_file(self, file_path, content, encoding):
        """处理成功打开的文件
        
        Args:
            file_path: 文件路径
            content: 文件内容
            encoding: 文件编码
            
        Returns:
            bool: 是否成功处理
        """
        # 创建新标签页框架
        tab_frame = tk.Frame(self.notebook)
        
        # 创建文本编辑区域
        text_area = self._create_text_widget(tab_frame)
        
        # 插入文件内容
        text_area.insert(1.0, content)
        
        # 添加标签页
        self.notebook.add(tab_frame, text=os.path.basename(file_path))
        
        # 切换到新标签页
        self.notebook.select(self.notebook.tabs()[-1])
        
        # 更新文件信息
        self.open_files.append({
            "path": file_path,
            "modified": False,
            "text_widget": text_area,
            "encoding": encoding
        })
        
        # 更新活动标签页索引
        self.active_tab_index = len(self.open_files) - 1
        
        # 更新窗口标题
        self._update_window_title()
        
        # 更新状态栏
        self.status_bar.config(text=f"编码: {encoding} | {self.status_bar.cget('text')}")
        
        # 重置修改标记
        text_area.edit_modified(False)
        
        return True
    
    def _handle_encoding_error(self, file_path, error_msg):
        """处理编码错误，提供编码选择对话框
        
        Args:
            file_path: 文件路径
            error_msg: 错误信息
            
        Returns:
            bool: 是否成功处理
        """
        # 创建编码选择对话框
        encoding_dialog = tk.Toplevel(self.root)
        encoding_dialog.title("选择文件编码")
        encoding_dialog.geometry("400x250")
        encoding_dialog.resizable(False, False)
        encoding_dialog.transient(self.root)
        encoding_dialog.grab_set()
        
        # 添加提示信息
        tk.Label(encoding_dialog, text=f"无法以默认编码打开文件：\n{os.path.basename(file_path)}\n\n请选择合适的编码：").pack(pady=10)
        
        # 常用编码列表
        encodings = ["UTF-8", "GB2312", "GBK", "UTF-16", "ASCII", "ISO-8859-1", "BIG5"]
        
        # 创建编码选择变量
        selected_encoding = tk.StringVar(encoding_dialog)
        selected_encoding.set(encodings[0])  # 默认选择UTF-8
        
        # 创建编码选择列表框
        encoding_listbox = tk.Listbox(encoding_dialog, height=7)
        for enc in encodings:
            encoding_listbox.insert(tk.END, enc)
        encoding_listbox.select_set(0)  # 默认选中第一项
        encoding_listbox.pack(fill=tk.X, padx=20, pady=5)
        
        # 对话框结果
        dialog_result = {"action": None, "encoding": None}
        
        # 确定按钮回调
        def confirm_encoding():
            selected_index = encoding_listbox.curselection()
            if selected_index:
                dialog_result["action"] = "confirm"
                dialog_result["encoding"] = encodings[selected_index[0]]
            encoding_dialog.destroy()
        
        # 取消按钮回调
        def cancel():
            dialog_result["action"] = "cancel"
            encoding_dialog.destroy()
        
        # 创建按钮
        button_frame = tk.Frame(encoding_dialog)
        button_frame.pack(pady=10)
        tk.Button(button_frame, text="确定", width=10, command=confirm_encoding).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="取消", width=10, command=cancel).pack(side=tk.LEFT, padx=10)
        
        # 等待对话框关闭
        self.root.wait_window(encoding_dialog)
        
        # 处理对话框结果
        if dialog_result["action"] == "confirm" and dialog_result["encoding"]:
            try:
                # 尝试使用选定的编码打开文件
                with open(file_path, 'r', encoding=dialog_result["encoding"]) as file:
                    content = file.read()
                return self._process_opened_file(file_path, content, dialog_result["encoding"])
            except Exception as e:
                messagebox.showerror("错误", f"使用 {dialog_result['encoding']} 编码打开文件失败: {str(e)}")
        
        return False
    
    def _save_file(self, event=None):
        """保存文件"""
        # 获取当前标签页信息
        if self.active_tab_index >= len(self.open_files):
            return False
            
        file_info = self.open_files[self.active_tab_index]
        file_path = file_info.get("path")
        text_area = file_info.get("text_widget")
        
        # 如果是新文件，调用另存为
        if file_path is None:
            return self._save_as_file()
        
        try:
            # 获取文本内容
            content = text_area.get(1.0, tk.END)
            
            # 使用文件处理器保存文件
            self.file_handler.save_file(file_path, content)
            
            # 更新文件状态
            file_info["modified"] = False
            
            # 更新窗口标题
            self._update_window_title()
            
            # 更新状态栏
            self.status_bar.config(text=f"已保存: {file_path}")
            
            # 重置修改标记
            text_area.edit_modified(False)
            
            return True
            
        except Exception as e:
            messagebox.showerror("错误", f"无法保存文件: {str(e)}")
            return False
    
    def _save_as_file(self, event=None):
        """文件另存为"""
        # 获取当前标签页信息
        if self.active_tab_index >= len(self.open_files):
            return False
            
        file_info = self.open_files[self.active_tab_index]
        text_area = file_info.get("text_widget")
        
        # 打开保存文件对话框
        file_path = filedialog.asksaveasfilename(
            title="保存文件",
            defaultextension=".txt",
            filetypes=[
                ("文本文件", "*.txt"),
                ("日志文件", "*.log"),
                ("所有文件", "*.*")
            ]
        )
        
        if not file_path:  # 用户取消操作
            return False
        
        try:
            # 获取文本内容
            content = text_area.get(1.0, tk.END)
            
            # 使用文件处理器保存文件
            self.file_handler.save_file(file_path, content)
            
            # 更新文件信息
            file_info["path"] = file_path
            file_info["modified"] = False
            
            # 更新标签页标题
            self.notebook.tab(self.active_tab_index, text=os.path.basename(file_path))
            
            # 更新窗口标题
            self._update_window_title()
            
            # 更新状态栏
            self.status_bar.config(text=f"已保存: {file_path}")
            
            # 重置修改标记
            text_area.edit_modified(False)
            
            return True
            
        except Exception as e:
            messagebox.showerror("错误", f"无法保存文件: {str(e)}")
            return False
    
    def _close_tab(self, tab_index):
        """关闭指定的标签页
        
        Args:
            tab_index: 要关闭的标签页索引
        """
        # 检查索引是否有效
        if tab_index < 0 or tab_index >= len(self.open_files):
            return False
        
        # 获取文件信息
        file_info = self.open_files[tab_index]
        text_area = file_info.get("text_widget")
        is_modified = file_info.get("modified", False)
        
        # 如果文件已修改，询问是否保存
        if is_modified:
            file_path = file_info.get("path")
            file_name = os.path.basename(file_path) if file_path else "无标题"
            
            response = messagebox.askyesnocancel(
                "记事本",
                f"是否将更改保存到 {file_name}？"
            )
            
            if response is None:  # 用户取消操作
                return False
            elif response:  # 用户选择保存
                # 切换到要关闭的标签页
                self.notebook.select(tab_index)
                self.active_tab_index = tab_index
                
                # 保存文件
                if not self._save_file():
                    return False
        
        # 关闭标签页
        self.notebook.forget(tab_index)
        
        # 从文件列表中移除
        self.open_files.pop(tab_index)
        
        # 更新活动标签页索引
        if self.open_files:
            # 如果关闭的是当前活动标签页，选择新的活动标签页
            if tab_index == self.active_tab_index:
                # 选择前一个标签页，如果没有则选择第一个
                new_index = max(0, tab_index - 1)
                self.notebook.select(new_index)
                self.active_tab_index = new_index
            # 如果关闭的标签页在当前活动标签页之前，更新活动标签页索引
            elif tab_index < self.active_tab_index:
                self.active_tab_index -= 1
        else:
            # 如果没有标签页了，创建一个新的
            self._new_file()
        
        # 更新窗口标题
        self._update_window_title()
        
        # 更新状态栏
        self._update_status_bar()
        
        return True
    
    def _close_other_tabs(self, keep_tab_index):
        """关闭除指定标签页外的所有标签页
        
        Args:
            keep_tab_index: 要保留的标签页索引
        """
        # 检查索引是否有效
        if keep_tab_index < 0 or keep_tab_index >= len(self.open_files):
            return False
        
        # 从后往前关闭标签页，避免索引变化问题
        for i in range(len(self.open_files) - 1, -1, -1):
            if i != keep_tab_index:
                if not self._close_tab(i):
                    # 如果有标签页关闭失败（用户取消），中断操作
                    return False
        
        return True
    
    def _open_in_explorer(self, tab_index):
        """在资源管理器中打开文件所在目录
        
        Args:
            tab_index: 标签页索引
        """
        # 检查索引是否有效
        if tab_index < 0 or tab_index >= len(self.open_files):
            return False
        
        # 获取文件路径
        file_path = self.open_files[tab_index].get("path")
        
        # 如果是新文件，没有路径
        if not file_path:
            messagebox.showinfo("记事本", "文件尚未保存，无法在资源管理器中打开。")
            return False
        
        # 获取文件所在目录
        file_dir = os.path.dirname(file_path)
        
        # 在资源管理器中打开
        try:
            os.startfile(file_dir)
            return True
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件所在目录: {str(e)}")
            return False
    
    def _on_text_modified(self, event=None, text_area=None):
        """文本修改事件处理
        
        Args:
            event: 事件对象
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        # 检查修改标记
        if text_area.edit_modified():
            # 查找对应的标签页索引
            tab_index = None
            for i, file_info in enumerate(self.open_files):
                if file_info.get("text_widget") == text_area:
                    tab_index = i
                    break
            
            if tab_index is not None:
                # 更新文件状态
                self.open_files[tab_index]["modified"] = True
                
                # 如果是当前活动标签页，更新窗口标题
                if tab_index == self.active_tab_index:
                    self._update_window_title()
            
            # 重置修改标记，避免重复触发
            text_area.edit_modified(False)
    
    def _update_status_bar(self, event=None, text_area=None):
        """更新状态栏信息
        
        Args:
            event: 事件对象
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        # 获取光标位置
        cursor_pos = text_area.index(tk.INSERT)
        line, column = cursor_pos.split('.')
        
        # 获取总行数
        total_lines = text_area.index(tk.END).split('.')[0]
        
        # 获取当前文件编码
        encoding = "UTF-8"  # 默认编码
        if self.active_tab_index < len(self.open_files):
            file_info = self.open_files[self.active_tab_index]
            if "encoding" in file_info:
                encoding = file_info["encoding"]
        
        # 更新状态栏
        self.status_bar.config(text=f"编码: {encoding} | 第 {line} 行，第 {int(column) + 1} 列 | 共 {int(total_lines) - 1} 行")
    
    def _on_closing(self):
        """窗口关闭事件处理"""
        # 检查是否有未保存的文件
        unsaved_files = []
        for i, file_info in enumerate(self.open_files):
            if file_info.get("modified", False):
                file_path = file_info.get("path")
                file_name = os.path.basename(file_path) if file_path else "无标题"
                unsaved_files.append((i, file_name))
        
        # 如果有未保存的文件，询问用户是否保存
        if unsaved_files:
            if len(unsaved_files) == 1:
                # 只有一个未保存的文件
                i, file_name = unsaved_files[0]
                
                response = messagebox.askyesnocancel(
                    "记事本",
                    f"是否将更改保存到 {file_name}？"
                )
                
                if response is None:  # 用户取消操作
                    return
                elif response:  # 用户选择保存
                    # 切换到要保存的标签页
                    self.notebook.select(i)
                    self.active_tab_index = i
                    
                    # 保存文件
                    if not self._save_file():
                        return
            else:
                # 多个未保存的文件
                # 创建自定义对话框
                dialog = tk.Toplevel(self.root)
                dialog.title("记事本")
                dialog.geometry("400x300")
                dialog.resizable(False, False)
                dialog.transient(self.root)
                dialog.grab_set()
                
                # 添加提示信息
                tk.Label(dialog, text="以下文件尚未保存，是否保存更改？").pack(pady=10)
                
                # 创建文件列表框架
                list_frame = tk.Frame(dialog)
                list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
                
                # 创建滚动条
                scrollbar = tk.Scrollbar(list_frame)
                scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
                
                # 创建列表框
                file_listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set)
                file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                scrollbar.config(command=file_listbox.yview)
                
                # 添加文件到列表框
                for i, file_name in unsaved_files:
                    file_listbox.insert(tk.END, file_name)
                
                # 默认全选
                for i in range(len(unsaved_files)):
                    file_listbox.selection_set(i)
                
                # 创建按钮框架
                button_frame = tk.Frame(dialog)
                button_frame.pack(pady=10)
                
                # 对话框结果
                dialog_result = {"action": None}
                
                # 保存按钮
                def save_selected():
                    dialog_result["action"] = "save"
                    dialog_result["selected"] = file_listbox.curselection()
                    dialog.destroy()
                
                # 不保存按钮
                def dont_save():
                    dialog_result["action"] = "dont_save"
                    dialog.destroy()
                
                # 取消按钮
                def cancel():
                    dialog_result["action"] = "cancel"
                    dialog.destroy()
                
                tk.Button(button_frame, text="保存选中项", width=12, command=save_selected).pack(side=tk.LEFT, padx=5)
                tk.Button(button_frame, text="不保存", width=12, command=dont_save).pack(side=tk.LEFT, padx=5)
                tk.Button(button_frame, text="取消", width=12, command=cancel).pack(side=tk.LEFT, padx=5)
                
                # 等待对话框关闭
                self.root.wait_window(dialog)
                
                # 处理对话框结果
                if dialog_result["action"] == "cancel" or dialog_result["action"] is None:
                    return
                elif dialog_result["action"] == "save":
                    # 保存选中的文件
                    selected_indices = dialog_result["selected"]
                    for idx in selected_indices:
                        i, _ = unsaved_files[idx]
                        
                        # 切换到要保存的标签页
                        self.notebook.select(i)
                        self.active_tab_index = i
                        
                        # 保存文件
                        if not self._save_file():
                            return
        
        # 保存配置
        self._save_config()
        
        # 销毁窗口
        self.root.destroy()

    def _bind_events(self):
        """绑定事件处理函数"""
        # 绑定窗口大小变化事件
        self.root.bind("<Configure>", lambda e: self._update_status_bar())
    
    def _apply_settings(self):
        """应用配置设置"""
        # 应用编辑器配置
        for file_info in self.open_files:
            text_area = file_info.get("text_widget")
            if text_area:
                self.editor.apply_config_to_text_widget(text_area)

    def _undo(self, text_area=None):
        """撤销操作
        
        Args:
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        try:
            text_area.edit_undo()
            # 更新状态栏
            self.status_bar.config(text="已撤销")
        except tk.TclError:
            # 没有可撤销的操作
            self.status_bar.config(text="没有可撤销的操作")
    
    def _redo(self, text_area=None):
        """恢复操作
        
        Args:
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        try:
            text_area.edit_redo()
            # 更新状态栏
            self.status_bar.config(text="已恢复")
        except tk.TclError:
            # 没有可恢复的操作
            self.status_bar.config(text="没有可恢复的操作")
    
    def _cut(self, text_area=None):
        """剪切操作
        
        Args:
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        # 使用编辑器模块的剪切功能
        self.editor.cut_text(text_area)
        
        # 更新状态栏
        self.status_bar.config(text="已剪切")
    
    def _copy(self, text_area=None):
        """复制操作
        
        Args:
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        # 使用编辑器模块的复制功能
        self.editor.copy_text(text_area)
        
        # 更新状态栏
        self.status_bar.config(text="已复制")
    
    def _paste(self, text_area=None):
        """粘贴操作
        
        Args:
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        # 使用编辑器模块的粘贴功能
        self.editor.paste_text(text_area)
        
        # 更新状态栏
        self.status_bar.config(text="已粘贴")
    
    def _delete(self, text_area=None):
        """删除操作
        
        Args:
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        # 使用编辑器模块的删除功能
        self.editor.delete_text(text_area)
        
        # 更新状态栏
        self.status_bar.config(text="已删除")
    
    def _select_all(self, text_area=None):
        """全选操作
        
        Args:
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        # 使用编辑器模块的全选功能
        self.editor.select_all(text_area)
        
        # 更新状态栏
        self.status_bar.config(text="已全选")
    
    def _insert_datetime(self, text_area=None):
        """插入当前日期和时间
        
        Args:
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        # 使用编辑器模块的插入日期时间功能
        self.editor.insert_datetime(text_area)
        
        # 更新状态栏
        self.status_bar.config(text="已插入时间/日期")


    def _find(self, text_area=None):
        """查找文本
        
        Args:
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        # 使用编辑器模块的查找功能
        self.editor.find_text(text_area, self.root)
    
    def _replace(self, text_area=None):
        """替换文本
        
        Args:
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        # 使用编辑器模块的替换功能
        self.editor.replace_text(text_area, self.root)
    
    def _toggle_word_wrap(self):
        """切换自动换行设置"""
        # 获取当前自动换行状态
        word_wrap = self.word_wrap_var.get()
        
        # 更新所有文本区域的自动换行设置
        for file_info in self.open_files:
            text_area = file_info.get("text_widget")
            if text_area:
                text_area.config(wrap=tk.WORD if word_wrap else tk.NONE)
        
        # 更新状态栏
        self.status_bar.config(text=f"自动换行: {'开启' if word_wrap else '关闭'}")
        
        # 保存配置
        self.config["word_wrap"] = word_wrap
        self._save_config()
    
    def _choose_font(self):
        """选择字体"""
        # 使用编辑器模块的字体选择功能
        selected_font = self.editor.choose_font(self.root)
        
        if selected_font:
            # 更新所有文本区域的字体设置
            for file_info in self.open_files:
                text_area = file_info.get("text_widget")
                if text_area:
                    text_area.config(font=selected_font)
            
            # 更新状态栏
            try:
                # 处理新的字体元组格式：(family, size, weight, slant, underline, overstrike)
                if len(selected_font) >= 6:
                    font_name, font_size, font_weight, font_slant, font_underline, font_overstrike = selected_font
                    
                    # 构建字体样式描述
                    style_desc = []
                    if font_weight == "bold":
                        style_desc.append("粗体")
                    if font_slant == "italic":
                        style_desc.append("斜体")
                    if font_underline:
                        style_desc.append("下划线")
                    if font_overstrike:
                        style_desc.append("删除线")
                    
                    style_text = "、".join(style_desc) if style_desc else "常规"
                    
                    self.status_bar.config(text=f"字体已更改: {font_name}, {font_size}pt, {style_text}")
                else:
                    # 兼容旧格式：(family, size)
                    font_name, font_size = selected_font[:2]
                    self.status_bar.config(text=f"字体已更改: {font_name}, {font_size}pt")
            except Exception as e:
                # 出现异常时使用简单格式显示
                self.status_bar.config(text=f"字体已更改")
                print(f"字体显示错误: {str(e)}")
    
    def _toggle_status_bar(self):
        """切换状态栏显示"""
        # 获取当前状态栏显示状态
        show_status_bar = self.status_bar_var.get()
        
        # 更新状态栏显示
        if show_status_bar:
            self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        else:
            self.status_bar.pack_forget()
    
    def _show_popup_menu(self, event, text_area=None):
        """显示右键菜单
        
        Args:
            event: 事件对象
            text_area: 文本区域控件，如果为None则使用当前活动标签页的文本区域
        """
        # 如果没有指定文本区域，使用当前活动标签页的文本区域
        if text_area is None:
            if self.active_tab_index >= len(self.open_files):
                return
            text_area = self.open_files[self.active_tab_index].get("text_widget")
        
        # 创建右键菜单
        popup_menu = tk.Menu(self.root, tearoff=0)
        popup_menu.add_command(label="撤销", command=lambda: self._undo(text_area), accelerator="Ctrl+Z")
        popup_menu.add_command(label="恢复", command=lambda: self._redo(text_area), accelerator="Ctrl+Y")
        popup_menu.add_separator()
        popup_menu.add_command(label="剪切", command=lambda: self._cut(text_area), accelerator="Ctrl+X")
        popup_menu.add_command(label="复制", command=lambda: self._copy(text_area), accelerator="Ctrl+C")
        popup_menu.add_command(label="粘贴", command=lambda: self._paste(text_area), accelerator="Ctrl+V")
        popup_menu.add_command(label="删除", command=lambda: self._delete(text_area), accelerator="Del")
        popup_menu.add_separator()
        popup_menu.add_command(label="全选", command=lambda: self._select_all(text_area), accelerator="Ctrl+A")
        
        # 显示菜单
        try:
            popup_menu.tk_popup(event.x_root, event.y_root, 0)
        finally:
            popup_menu.grab_release()
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = """记事本使用帮助：
        
文件操作：
- 新建文件：Ctrl+N
- 打开文件：Ctrl+O
- 保存文件：Ctrl+S
- 另存为：Ctrl+Shift+S

编辑操作：
- 撤销：Ctrl+Z
- 恢复：Ctrl+Y
- 剪切：Ctrl+X
- 复制：Ctrl+C
- 粘贴：Ctrl+V
- 删除：Del
- 查找：Ctrl+F
- 替换：Ctrl+H
- 全选：Ctrl+A
- 插入时间/日期：F5

格式设置：
- 自动换行：可在"格式"菜单中切换
- 字体设置：可在"格式"菜单中设置

查看设置：
- 状态栏：可在"查看"菜单中切换显示/隐藏
        """
        
        # 创建帮助对话框
        help_dialog = tk.Toplevel(self.root)
        help_dialog.title("记事本帮助")
        help_dialog.geometry("500x400")
        help_dialog.resizable(True, True)
        help_dialog.transient(self.root)
        help_dialog.grab_set()
        
        # 创建文本区域显示帮助信息
        help_text_area = tk.Text(help_dialog, wrap=tk.WORD, padx=10, pady=10)
        help_text_area.pack(fill=tk.BOTH, expand=True)
        help_text_area.insert(1.0, help_text)
        help_text_area.config(state=tk.DISABLED)  # 设置为只读
        
        # 创建滚动条
        scrollbar = tk.Scrollbar(help_text_area, command=help_text_area.yview)
        help_text_area.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建关闭按钮
        tk.Button(help_dialog, text="关闭", command=help_dialog.destroy).pack(pady=10)
    
    def _show_about(self):
        """显示关于信息"""
        about_text = """记事本 v1.0
        
一个简单的文本编辑器，支持基本的文本编辑功能。

功能特点：
- 多标签页编辑
- 文件的打开、保存
- 文本的编辑、查找、替换
- 字体和格式设置

开发者：Python爱好者
        """
        
        # 显示关于对话框
        messagebox.showinfo("关于记事本", about_text)
    
    def _highlight_search_results(self, text_area, search_text, case_sensitive=False):
        """高亮显示搜索结果
        
        Args:
            text_area: 文本区域控件
            search_text: 要搜索的文本
            case_sensitive: 是否区分大小写
        """
        # 清除之前的高亮
        text_area.tag_remove("search", "1.0", tk.END)
        
        if not search_text:
            return
        
        # 设置高亮标签
        text_area.tag_configure("search", background="yellow", foreground="black")
        
        # 搜索文本
        start_pos = "1.0"
        while True:
            # 查找下一个匹配位置
            if case_sensitive:
                start_pos = text_area.search(search_text, start_pos, stopindex=tk.END, nocase=0)
            else:
                start_pos = text_area.search(search_text, start_pos, stopindex=tk.END, nocase=1)
            
            if not start_pos:
                break
            
            # 计算结束位置
            end_pos = f"{start_pos}+{len(search_text)}c"
            
            # 添加高亮标签
            text_area.tag_add("search", start_pos, end_pos)
            
            # 更新起始位置
            start_pos = end_pos
    
    def _update_line_numbers(self, text_area, line_numbers):
        """更新行号
        
        Args:
            text_area: 文本区域控件
            line_numbers: 行号文本区域控件
        """
        # 获取文本内容的行数
        text_content = text_area.get("1.0", tk.END)
        line_count = text_content.count('\n') + (1 if text_content[-1] != '\n' else 0)
        
        # 生成行号文本
        line_numbers_text = '\n'.join(str(i) for i in range(1, line_count + 1))
        
        # 更新行号文本区域
        line_numbers.config(state='normal')
        line_numbers.delete("1.0", tk.END)
        line_numbers.insert("1.0", line_numbers_text)
        line_numbers.config(state='disabled')
        
        # 同步滚动位置
        line_numbers.yview_moveto(text_area.yview()[0])

    def _load_config(self):
        """加载配置文件
        
        Returns:
            配置字典
        """
        # 配置文件路径
        config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config")
        config_path = os.path.join(config_dir, "notepad_settings.json")
        
        # 默认配置
        default_config = {
            "word_wrap": True,
            "show_status_bar": True,
            "font_family": "宋体",
            "font_size": 12,
            "window_width": 800,
            "window_height": 600
        }
        
        # 如果配置文件存在，读取配置
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return {**default_config, **config}  # 合并默认配置和已保存配置
            except Exception as e:
                messagebox.showwarning("警告", f"读取配置文件失败: {str(e)}\n将使用默认配置。")
                return default_config
        else:
            # 确保配置目录存在
            os.makedirs(config_dir, exist_ok=True)
            return default_config

    def _save_config(self):
        """保存配置到文件"""
        # 更新配置
        self.config["word_wrap"] = self.word_wrap_var.get()
        self.config["show_status_bar"] = self.status_bar_var.get()
        
        # 配置文件路径
        config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config")
        config_path = os.path.join(config_dir, "notepad_settings.json")
        
        # 确保配置目录存在
        os.makedirs(config_dir, exist_ok=True)
        
        # 保存配置
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            messagebox.showwarning("警告", f"保存配置文件失败: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = NotepadApp(root)
    root.mainloop()