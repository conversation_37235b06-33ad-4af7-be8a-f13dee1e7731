import os, sys, logging, traceback
from datetime import datetime
from logging.handlers import RotatingFileHandler
from functools import wraps

# 创建日志目录
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "logs")
os.makedirs(log_dir, exist_ok=True)

# 配置日志
logger = logging.getLogger("notepad")
logger.setLevel(logging.INFO)

# 防止重复添加处理器
if not logger.handlers:
    # 文件处理器 - 使用RotatingFileHandler实现日志滚动
    log_file = os.path.join(log_dir, f"notepad_{datetime.now().strftime('%Y%m%d')}.log")
    file_handler = RotatingFileHandler(
        log_file, 
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    file_handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s'))
    logger.addHandler(file_handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s'))
    logger.addHandler(console_handler)

# 导出日志函数
def debug(message): logger.debug(message)
def info(message): logger.info(message)
def warning(message): logger.warning(message)
def error(message, exc_info=None): logger.error(message, exc_info=exc_info)
def critical(message, exc_info=None): logger.critical(message, exc_info=exc_info)

def log_exception(message="发生异常"):
    """记录当前异常信息
    
    Args:
        message: 错误前缀消息，默认为"发生异常"
    """
    exc_type, exc_value, exc_traceback = sys.exc_info()
    if exc_type:
        stack_trace = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logger.error(f"{message}:\n{stack_trace}")

def error_handler(show_error_dialog=True):
    """错误处理装饰器，用于捕获和记录函数执行过程中的异常
    
    Args:
        show_error_dialog: 是否显示错误对话框
    
    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 记录异常
                log_exception(f"在执行 {func.__name__} 时发生错误")
                
                # 显示错误对话框
                if show_error_dialog:
                    try:
                        import tkinter.messagebox as messagebox
                        messagebox.showerror("错误", f"操作失败: {str(e)}\n详细信息已记录到日志文件。")
                    except ImportError:
                        # 非GUI环境，直接打印错误
                        print(f"错误: {str(e)}")
                
                # 根据具体情况返回默认值
                return None
                
        return wrapper
    return decorator

# 设置未捕获异常处理器
def handle_uncaught_exception(exc_type, exc_value, exc_traceback):
    """处理未捕获的异常
    
    Args:
        exc_type: 异常类型
        exc_value: 异常值
        exc_traceback: 异常追踪
    """
    if issubclass(exc_type, KeyboardInterrupt):
        # 不处理键盘中断
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    # 记录未捕获的异常
    logger.critical("未捕获的异常:", exc_info=(exc_type, exc_value, exc_traceback))
    
    # 尝试显示错误对话框
    try:
        import tkinter.messagebox as messagebox
        messagebox.showerror("严重错误", 
                           f"应用程序遇到未处理的错误: {str(exc_value)}\n"
                           f"详细信息已记录到日志文件。\n"
                           f"应用程序即将关闭。")
    except:
        pass

# 设置全局异常处理器
sys.excepthook = handle_uncaught_exception

# 记录应用启动
info("===== 记事本应用启动 =====")