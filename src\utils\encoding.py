import chardet
import codecs
import os

class EncodingDetector:
    """
    编码检测工具类，用于检测文件编码和处理编码转换
    """
    
    # 常用编码列表
    COMMON_ENCODINGS = [
        'utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'gb18030', 
        'big5', 'shift_jis', 'euc-jp', 'euc-kr',
        'iso-8859-1', 'latin-1', 'ascii'
    ]
    
    @staticmethod
    def detect_encoding(file_path):
        """
        检测文件编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 检测到的编码名称，如果检测失败则返回None
        """
        if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
            return 'utf-8'  # 默认使用UTF-8编码
        
        # 读取文件的前4096个字节用于检测编码
        with open(file_path, 'rb') as f:
            raw_data = f.read(4096)
            if not raw_data:
                return 'utf-8'
        
        # 使用chardet检测编码
        result = chardet.detect(raw_data)
        encoding = result['encoding']
        confidence = result['confidence']
        
        # 如果置信度较低，尝试常用编码
        if confidence < 0.7:
            for enc in EncodingDetector.COMMON_ENCODINGS:
                try:
                    with codecs.open(file_path, 'r', encoding=enc) as f:
                        f.read(100)  # 尝试读取一小部分内容
                    return enc
                except UnicodeDecodeError:
                    continue
        
        return encoding or 'utf-8'
    
    @staticmethod
    def read_file(file_path, encoding=None):
        """
        使用指定编码读取文件内容
        
        Args:
            file_path: 文件路径
            encoding: 指定编码，如果为None则自动检测
            
        Returns:
            tuple: (文件内容, 使用的编码)
        """
        if not encoding:
            encoding = EncodingDetector.detect_encoding(file_path)
        
        try:
            with codecs.open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            return content, encoding
        except UnicodeDecodeError:
            # 如果指定编码失败，尝试自动检测
            encoding = EncodingDetector.detect_encoding(file_path)
            with codecs.open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            return content, encoding
    
    @staticmethod
    def write_file(file_path, content, encoding='utf-8'):
        """
        使用指定编码写入文件内容
        
        Args:
            file_path: 文件路径
            content: 文件内容
            encoding: 指定编码，默认为UTF-8
            
        Returns:
            bool: 写入是否成功
        """
        try:
            with codecs.open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"写入文件失败: {e}")
            return False
    
    @staticmethod
    def convert_encoding(content, from_encoding, to_encoding):
        """
        转换文本内容的编码
        
        Args:
            content: 文本内容
            from_encoding: 原编码
            to_encoding: 目标编码
            
        Returns:
            str: 转换后的文本内容
        """
        if from_encoding.lower() == to_encoding.lower():
            return content
            
        try:
            # 将内容转换为字节，然后再解码为目标编码
            if isinstance(content, str):
                content_bytes = content.encode(from_encoding)
            else:
                content_bytes = content
                
            return content_bytes.decode(to_encoding)
        except Exception as e:
            print(f"编码转换失败: {e}")
            return content  # 转换失败返回原内容
    
    @staticmethod
    def get_available_encodings():
        """
        获取可用的编码列表
        
        Returns:
            list: 常用编码列表
        """
        return EncodingDetector.COMMON_ENCODINGS